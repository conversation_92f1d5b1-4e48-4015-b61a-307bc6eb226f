<view class="sell-container"><view class="header-section"><text class="main-title">🚗 快速卖车</text><text class="sub-title">3分钟填写，立即获得专业估价</text></view><view class="form-wrapper"><view class="info-block"><view class="block-title"><text class="title-icon">🚙</text><text class="title-text">车辆信息</text></view><view class="input-grid"><view class="input-row"><view class="input-item full"><text class="label">品牌车型</text><picker mode="selector" range="{{carModels}}" data-event-opts="{{[['change',[['selectCarModel',['$event']]]]]}}" bindchange="__e"><view class="picker-box"><text class="picker-text">{{carData.brand||'选择车型'}}</text><text class="picker-arrow">▼</text></view></picker></view></view><view class="input-row"><view class="input-item"><text class="label">购买年份</text><picker mode="selector" range="{{years}}" data-event-opts="{{[['change',[['selectYear',['$event']]]]]}}" bindchange="__e"><view class="picker-box"><text class="picker-text">{{carData.year||'选择年份'}}</text><text class="picker-arrow">▼</text></view></picker></view><view class="input-item"><text class="label">行驶里程</text><view class="input-box"><input class="text-input" type="number" placeholder="万公里" data-event-opts="{{[['input',[['__set_model',['$0','mileage','$event',[]],['carData']]]]]}}" value="{{carData.mileage}}" bindinput="__e"/></view></view></view></view></view><view class="info-block"><view class="block-title"><text class="title-icon">⭐</text><text class="title-text">车况信息</text></view><view class="input-grid"><view class="input-row"><view class="input-item full"><text class="label">车况等级</text><view class="option-group"><view data-event-opts="{{[['tap',[['selectCondition',['精品']]]]]}}" class="{{['option-item',(carData.condition==='精品')?'selected':'']}}" bindtap="__e"><text class="option-emoji">✨</text><text class="option-label">精品</text></view><view data-event-opts="{{[['tap',[['selectCondition',['良好']]]]]}}" class="{{['option-item',(carData.condition==='良好')?'selected':'']}}" bindtap="__e"><text class="option-emoji">👍</text><text class="option-label">良好</text></view><view data-event-opts="{{[['tap',[['selectCondition',['一般']]]]]}}" class="{{['option-item',(carData.condition==='一般')?'selected':'']}}" bindtap="__e"><text class="option-emoji">🔧</text><text class="option-label">一般</text></view></view></view></view><view class="input-row"><view class="input-item"><text class="label">变速箱</text><view class="toggle-group"><view data-event-opts="{{[['tap',[['selectTransmission',['自动']]]]]}}" class="{{['toggle-item',(carData.transmission==='自动')?'active':'']}}" bindtap="__e">自动</view><view data-event-opts="{{[['tap',[['selectTransmission',['手动']]]]]}}" class="{{['toggle-item',(carData.transmission==='手动')?'active':'']}}" bindtap="__e">手动</view></view></view><view class="input-item"><text class="label">所在城市</text><view class="input-box"><input class="text-input" placeholder="如：北京" data-event-opts="{{[['input',[['__set_model',['$0','city','$event',[]],['carData']]]]]}}" value="{{carData.city}}" bindinput="__e"/></view></view></view></view></view><view class="info-block"><view class="block-title"><text class="title-icon">📱</text><text class="title-text">联系信息</text></view><view class="input-grid"><view class="input-row"><view class="input-item"><text class="label">您的姓名</text><view class="input-box"><input class="text-input" placeholder="称呼" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['carData']]]]]}}" value="{{carData.name}}" bindinput="__e"/></view></view><view class="input-item"><text class="label">手机号码</text><view class="input-box"><input class="text-input" type="number" placeholder="手机号" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['carData']]]]]}}" value="{{carData.phone}}" bindinput="__e"/></view></view></view><view class="input-row"><view class="input-item full"><text class="label">期望价格</text><view class="input-box price-input"><input class="text-input" type="number" placeholder="万元" data-event-opts="{{[['input',[['__set_model',['$0','price','$event',[]],['carData']]]]]}}" value="{{carData.price}}" bindinput="__e"/><text class="price-unit">万元</text></view></view></view></view></view></view><view class="bottom-bar"><view data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="submit-btn" bindtap="__e"><text class="btn-text">💰 立即获取估价</text></view></view></view>