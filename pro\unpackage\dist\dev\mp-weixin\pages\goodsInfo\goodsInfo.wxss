@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.car-detail {
  background: #f6f7fb;
  min-height: 100vh;
}
.section {
  margin: 12px;
}
.card {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.car-name {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  flex: 1;
}
.title-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
.favorite-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.favorite-btn.active {
  background: rgba(255, 77, 79, 0.1);
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.favorite-btn:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.favorite-icon {
  font-size: 20px;
  transition: all 0.3s ease;
}
.car-price {
  color: #ff4d4f;
  font-weight: 700;
  font-size: 18px;
}
.tags {
  display: flex;
  gap: 6px;
  margin-top: 6px;
  flex-wrap: wrap;
}
.meta {
  display: flex;
  gap: 10px;
  margin-top: 6px;
  font-size: 12px;
  color: #666;
}
.sub-title {
  font-size: 15px;
  color: #666;
  margin-bottom: 8px;
}
.specs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px 12px;
}
.spec-item {
  display: flex;
  justify-content: space-between;
  color: #333;
}
.spec-item .k {
  color: #666;
}
.seller {
  display: flex;
  align-items: center;
  gap: 12px;
}
.seller-info {
  display: flex;
  flex-direction: column;
}
.seller-name {
  font-weight: 600;
}
.seller-phone {
  color: #666;
  font-size: 12px;
}
.mt8 {
  margin-top: 8px;
}
