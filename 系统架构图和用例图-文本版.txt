═══════════════════════════════════════════════════════════════════════════════
                    心理健康关怀平台 - 系统总体架构图
═══════════════════════════════════════════════════════════════════════════════

┌─────────────────────────────────────────────────────────────────────────────┐
│                              用户层                                         │
│                 普通用户  |  心理辅导员  |  后台管理员                      │
└─────────────────────────────┬───────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                          前端表现层                                         │
│                    Vue.js框架 + 响应式页面设计                             │
│                        用户交互体验优化                                     │
└─────────────────────────────┬───────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                         业务逻辑层                                          │
│                      Spring Boot框架                                       │
│                   业务逻辑处理 + API接口                                    │
└─────────────────────────────┬───────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                         数据访问层                                          │
│                       MyBatis/JPA                                          │
│                      数据库交互处理                                         │
└─────────────────────────────┬───────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                          数据库层                                           │
│                        MySQL数据库                                          │
│                       数据持久化存储                                        │
└─────────────────────────────────────────────────────────────────────────────┘

═══════════════════════════════════════════════════════════════════════════════
                           六大核心功能模块
═══════════════════════════════════════════════════════════════════════════════

┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   用户模块      │  │  心理测评模块   │  │  情绪日记模块   │
│   ─────────     │  │   ─────────     │  │   ─────────     │
│   • 注册登录    │  │   • 量表测试    │  │   • 情绪记录    │
│   • 角色管理    │  │   • 报告生成    │  │   • 回顾分析    │
└─────────────────┘  └─────────────────┘  └─────────────────┘

┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  树洞互助模块   │  │  心理知识模块   │  │  后台管理模块   │
│   ─────────     │  │   ─────────     │  │   ─────────     │
│   • 匿名倾诉    │  │   • 知识推送    │  │   • 数据管理    │
│   • 互相鼓励    │  │   • 案例分享    │  │   • 内容审核    │
└─────────────────┘  └─────────────────┘  └─────────────────┘

═══════════════════════════════════════════════════════════════════════════════
                              核心数据表
═══════════════════════════════════════════════════════════════════════════════

用户信息表  |  心理测评结果表  |  情绪日记表  |  社区互动表  |  心理知识表  |  系统日志表


═══════════════════════════════════════════════════════════════════════════════
                    心理健康关怀平台 - 用例图
═══════════════════════════════════════════════════════════════════════════════

系统角色：
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  普通用户   │    │ 心理辅导员  │    │ 后台管理员  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   │                   │
       ▼                   ▼                   ▼

普通用户功能：
├─ 用户注册登录
├─ 个人信息管理
├─ 心理测评 ──────────► 查看测评报告
├─ 情绪日记记录 ──────► 情绪日记回顾
├─ 匿名树洞倾诉
├─ 互助留言鼓励
├─ 浏览心理知识
└─ 案例分享

心理辅导员功能：
├─ 用户注册登录
├─ 个人信息管理
├─ 辅导员资料管理
├─ 查看用户测评
├─ 提供心理指导
├─ 浏览心理知识
└─ 案例分享

后台管理员功能：
├─ 用户注册登录
├─ 用户数据管理
├─ 内容审核
└─ 数据统计分析

═══════════════════════════════════════════════════════════════════════════════
                              功能关系说明
═══════════════════════════════════════════════════════════════════════════════

扩展关系：
• 心理测评 → 查看测评报告
• 情绪日记记录 → 情绪日记回顾

包含关系：
• 所有功能都包含用户身份验证
• 管理功能包含权限验证

继承关系：
• 所有角色都继承基本的登录注册功能
• 心理辅导员和管理员继承普通用户的部分功能

═══════════════════════════════════════════════════════════════════════════════
                              技术架构特点
═══════════════════════════════════════════════════════════════════════════════

✓ 前后端分离：Vue.js + Spring Boot，提高开发效率
✓ 响应式设计：适配多种设备，提升用户体验
✓ 模块化设计：六大功能模块，便于维护和扩展
✓ 数据安全：用户隐私保护，匿名功能支持
✓ 角色权限：三级用户权限管理，功能访问控制
