<view class="car-detail"><u-swiper vue-id="0f939476-1" list="{{images}}" height="240" indicator="{{true}}" circular="{{true}}" bind:__l="__l"></u-swiper><view class="section card"><view class="title-row"><text class="car-name">{{car.name}}</text><view class="title-actions"><view data-event-opts="{{[['tap',[['toggleFavorite',['$event']]]]]}}" class="{{['favorite-btn',(isFavorited)?'active':'']}}" bindtap="__e"><text class="favorite-icon">{{isFavorited?'❤️':'🤍'}}</text></view><text class="car-price">{{"￥"+$root.m0}}</text></view></view><view class="tags"><block wx:for="{{car.tags}}" wx:for-item="t" wx:for-index="i" wx:key="i"><u-tag class="tag" vue-id="{{'0f939476-2-'+i}}" text="{{t}}" size="mini" type="primary" plain="{{true}}" bind:__l="__l"></u-tag></block></view><view class="meta"><text>{{car.year+"年"}}</text><text>{{car.mileage+"万公里"}}</text><text>{{car.city}}</text></view></view><view class="section card"><view class="sub-title">车辆参数</view><view class="specs"><view class="spec-item"><text class="k">车况</text><text class="v">{{car.condition}}</text></view><view class="spec-item"><text class="k">排放标准</text><text class="v">{{car.emission}}</text></view><view class="spec-item"><text class="k">变速箱</text><text class="v">{{car.transmission}}</text></view><view class="spec-item"><text class="k">排量/驱动</text><text class="v">{{car.displacement}}</text></view><view class="spec-item"><text class="k">过户次数</text><text class="v">{{car.ownerships}}</text></view></view></view><view class="section card"><view class="sub-title">购买流程</view><u-steps vue-id="0f939476-3" current="{{1}}" bind:__l="__l" vue-slots="{{['default']}}"><u-steps-item vue-id="{{('0f939476-4')+','+('0f939476-3')}}" title="咨询车况" desc="在线或电话咨询" bind:__l="__l"></u-steps-item><u-steps-item vue-id="{{('0f939476-5')+','+('0f939476-3')}}" title="预约看车" desc="到店/上门验车" bind:__l="__l"></u-steps-item><u-steps-item vue-id="{{('0f939476-6')+','+('0f939476-3')}}" title="签约订车" desc="定金锁车，办理过户" bind:__l="__l"></u-steps-item><u-steps-item vue-id="{{('0f939476-7')+','+('0f939476-3')}}" title="交付提车" desc="完成尾款，交付车辆" bind:__l="__l"></u-steps-item></u-steps></view><view class="section card"><view class="sub-title">联系卖家</view><view class="seller"><u-avatar vue-id="0f939476-8" src="{{seller.avatar}}" size="46" bind:__l="__l"></u-avatar><view class="seller-info"><text class="seller-name">{{seller.name}}</text><text class="seller-phone">{{maskedPhone}}</text></view><u-button vue-id="0f939476-9" type="primary" size="small" data-event-opts="{{[['^click',[['callPhone']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">拨打</u-button></view><u-button class="mt8" vue-id="0f939476-10" shape="circle" data-event-opts="{{[['^click',[['reserve']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">预约看车</u-button><u-button class="mt8" vue-id="0f939476-11" type="success" shape="circle" data-event-opts="{{[['^click',[['book']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">立即订车</u-button></view></view>