@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.buy-page {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  min-height: 100vh;
}
/* 搜索区域 */
.search-section {
  background: #ffffff;
  padding: 16px;
  border-radius: 0 0 24px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
/* 筛选器区域 */
.filter-section {
  padding: 16px 0 8px;
}
.filter-section .filter-scroll {
  white-space: nowrap;
}
.filter-section .filter-list {
  display: flex;
  gap: 12px;
  padding: 0 16px;
}
.filter-section .filter-list .filter-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 8px 16px;
  white-space: nowrap;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.filter-section .filter-list .filter-chip.active {
  background: #1890ff;
  border-color: #1890ff;
}
.filter-section .filter-list .filter-chip.active .filter-text, .filter-section .filter-list .filter-chip.active .filter-arrow {
  color: #ffffff;
}
.filter-section .filter-list .filter-chip.active .filter-icon {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
}
.filter-section .filter-list .filter-chip .filter-icon {
  font-size: 14px;
}
.filter-section .filter-list .filter-chip .filter-text {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}
.filter-section .filter-list .filter-chip .filter-arrow {
  font-size: 12px;
  color: #8c8c8c;
}
/* 结果统计 */
.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px 16px;
}
.result-header .result-count {
  font-size: 14px;
  color: #595959;
}
.result-header .sort-options {
  display: flex;
  align-items: center;
  gap: 4px;
}
.result-header .sort-options .sort-text {
  font-size: 14px;
  color: #1890ff;
}
.result-header .sort-options .sort-icon {
  font-size: 14px;
  color: #1890ff;
}
/* 车辆列表 */
.car-list {
  padding: 0 16px 24px;
}
.car-card {
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.car-card:active {
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.car-card .car-image-wrapper {
  position: relative;
  height: 180px;
}
.car-card .car-image-wrapper .car-image {
  width: 100%;
  height: 100%;
}
.car-card .car-image-wrapper .car-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.car-card .car-content {
  padding: 16px;
}
.car-card .car-content .car-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}
.car-card .car-content .car-header .car-title {
  font-size: 18px;
  font-weight: 700;
  color: #262626;
  flex: 1;
  margin-right: 12px;
  line-height: 1.3;
}
.car-card .car-content .car-header .car-price {
  font-size: 20px;
  font-weight: 700;
  color: #ff4d4f;
  white-space: nowrap;
}
.car-card .car-content .car-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}
.car-card .car-content .car-meta .meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.car-card .car-content .car-meta .meta-item .meta-icon {
  font-size: 12px;
}
.car-card .car-content .car-meta .meta-item .meta-text {
  font-size: 13px;
  color: #595959;
}
.car-card .car-content .car-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}
.car-card .car-content .car-tags .modern-tag {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
}
.car-card .car-content .car-description .desc-text {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
}
/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}
.empty-state .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}
.empty-state .empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  display: block;
}
.empty-state .empty-subtitle {
  font-size: 14px;
  color: #595959;
  display: block;
}
