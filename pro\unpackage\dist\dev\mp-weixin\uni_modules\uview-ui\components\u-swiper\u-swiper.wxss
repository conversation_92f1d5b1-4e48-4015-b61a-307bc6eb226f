@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
view.data-v-6b019429, scroll-view.data-v-6b019429, swiper-item.data-v-6b019429 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-swiper.data-v-6b019429 {

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.u-swiper__wrapper.data-v-6b019429 {
  flex: 1;
}
.u-swiper__wrapper__item.data-v-6b019429 {
  flex: 1;
}
.u-swiper__wrapper__item__wrapper.data-v-6b019429 {

  display: flex;

  flex-direction: row;
  position: relative;
  overflow: hidden;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
  flex: 1;
}
.u-swiper__wrapper__item__wrapper__image.data-v-6b019429 {
  flex: 1;
}
.u-swiper__wrapper__item__wrapper__video.data-v-6b019429 {
  flex: 1;
}
.u-swiper__wrapper__item__wrapper__title.data-v-6b019429 {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
  bottom: 0;
  left: 0;
  right: 0;
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  color: #FFFFFF;
  flex: 1;
}
.u-swiper__indicator.data-v-6b019429 {
  position: absolute;
  bottom: 10px;
}
