@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.sell-home {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}
.hero-section {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  padding: 60px 20px 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}
.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}
.hero-section .hero-content {
  flex: 1;
  z-index: 2;
}
.hero-section .hero-content .hero-title {
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  display: block;
  margin-bottom: 8px;
}
.hero-section .hero-content .hero-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  margin-bottom: 12px;
}
.hero-section .hero-content .hero-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  line-height: 1.4;
}
.hero-section .hero-decoration {
  z-index: 2;
}
.hero-section .hero-decoration .decoration-icon {
  font-size: 60px;
  opacity: 0.8;
}
.features-section {
  padding: 40px 20px;
}
.features-section .section-title {
  text-align: center;
  margin-bottom: 30px;
}
.features-section .section-title .title-text {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}
.features-section .features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}
.features-section .features-grid .feature-item {
  background: #ffffff;
  padding: 24px 16px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.features-section .features-grid .feature-item:active {
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}
.features-section .features-grid .feature-item .feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}
.features-section .features-grid .feature-item .feature-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: block;
  margin-bottom: 8px;
}
.features-section .features-grid .feature-item .feature-desc {
  font-size: 12px;
  color: #8c8c8c;
  display: block;
  line-height: 1.4;
}
.action-section {
  padding: 20px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.05);
}
.action-section .valuation-btn {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}
.action-section .valuation-btn .btn-text {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}
.action-section .tips {
  text-align: center;
  margin-top: 12px;
}
.action-section .tips .tips-text {
  font-size: 12px;
  color: #8c8c8c;
}
