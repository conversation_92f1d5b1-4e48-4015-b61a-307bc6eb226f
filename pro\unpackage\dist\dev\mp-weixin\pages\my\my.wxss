@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.my-page {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  min-height: 100vh;
}
/* 用户信息区域 */
.profile-section {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  padding: 24px 16px 32px;
  border-radius: 0 0 32px 32px;
  position: relative;
}
.profile-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}
.profile-section .profile-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
  z-index: 1;
}
.profile-section .profile-header .profile-avatar {
  position: relative;
}
.profile-section .profile-header .profile-avatar .avatar-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.profile-section .profile-header .profile-info {
  flex: 1;
}
.profile-section .profile-header .profile-info .profile-name {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  display: block;
  margin-bottom: 4px;
}
.profile-section .profile-header .profile-info .profile-phone {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  margin-bottom: 16px;
}
.profile-section .profile-header .profile-info .profile-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}
.profile-section .profile-header .profile-info .profile-stats .stat-item {
  text-align: center;
}
.profile-section .profile-header .profile-info .profile-stats .stat-item .stat-number {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  display: block;
}
.profile-section .profile-header .profile-info .profile-stats .stat-item .stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  margin-top: 2px;
}
.profile-section .profile-header .profile-info .profile-stats .stat-divider {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.3);
}
.profile-section .profile-header .profile-edit {
  padding: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
}
.profile-section .profile-header .profile-edit:active {
  background: rgba(255, 255, 255, 0.3);
}
.profile-section .profile-header .profile-edit .edit-icon {
  font-size: 16px;
}
/* 快捷功能 */
.quick-functions {
  margin: -16px 16px 24px;
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.quick-functions .function-grid {
  display: flex;
  justify-content: space-between;
}
.quick-functions .function-grid .function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}
.quick-functions .function-grid .function-item .function-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.quick-functions .function-grid .function-item .function-icon.buy-icon {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}
.quick-functions .function-grid .function-item .function-icon.sell-icon {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}
.quick-functions .function-grid .function-item .function-icon.favorite-icon {
  background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
}
.quick-functions .function-grid .function-item .function-icon.history-icon {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
}
.quick-functions .function-grid .function-item .function-icon .icon-text {
  font-size: 20px;
}
.quick-functions .function-grid .function-item .function-text {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
}
.quick-functions .function-grid .function-item:active .function-icon {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 区块卡片 */
.section-card {
  background: #ffffff;
  border-radius: 16px;
  margin: 0 16px 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.section-card .section-header {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 16px;
}
.section-card .section-header .section-title .title-text {
  font-size: 18px;
  font-weight: 700;
  color: #262626;
  display: block;
}
.section-card .section-header .section-title .title-subtitle {
  font-size: 14px;
  color: #595959;
  margin-top: 4px;
  display: block;
}
.section-card .section-header .section-more {
  display: flex;
  align-items: center;
  gap: 4px;
}
.section-card .section-header .section-more .more-text {
  font-size: 14px;
  color: #1890ff;
}
.section-card .section-header .section-more .more-icon {
  font-size: 14px;
  color: #1890ff;
}
/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.order-list .order-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  transition: all 0.3s ease;
}
.order-list .order-item:active {
  background: #f5f5f5;
}
.order-list .order-item .order-icon {
  width: 40px;
  height: 40px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.order-list .order-item .order-icon .icon-emoji {
  font-size: 18px;
}
.order-list .order-item .order-content {
  flex: 1;
}
.order-list .order-item .order-content .order-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: block;
  margin-bottom: 4px;
}
.order-list .order-item .order-content .order-desc {
  font-size: 14px;
  color: #595959;
  display: block;
}
.order-list .order-item .order-arrow {
  font-size: 16px;
  color: #8c8c8c;
}
/* 服务列表 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.service-list .service-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  transition: all 0.3s ease;
}
.service-list .service-item:active {
  background: #f5f5f5;
}
.service-list .service-item .service-icon {
  width: 40px;
  height: 40px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service-list .service-item .service-icon .icon-emoji {
  font-size: 18px;
}
.service-list .service-item .service-content {
  flex: 1;
}
.service-list .service-item .service-content .service-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: block;
  margin-bottom: 4px;
}
.service-list .service-item .service-content .service-desc {
  font-size: 14px;
  color: #595959;
  display: block;
}
.service-list .service-item .service-arrow {
  font-size: 16px;
  color: #8c8c8c;
}
