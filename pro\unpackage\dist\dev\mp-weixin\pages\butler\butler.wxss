
.profit-page { background: #f6f7fb; min-height: 100vh;
}
.summary {
	margin: 12px;
	background: linear-gradient(135deg, #1f6feb, #3ba1ff);
	border-radius: 12px;
	padding: 14px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #fff;
}
.summary-item { display: flex; flex-direction: column; align-items: center;
}
.label { font-size: 12px; opacity: 0.9;
}
.value { font-size: 18px; font-weight: 700; margin-top: 4px;
}
.highlight { color: #ffe58f;
}
.divider { width: 1px; height: 38px; background: rgba(255,255,255,0.35);
}
.card { background: #fff; border-radius: 12px; margin: 12px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.card-title { padding: 12px; font-size: 14px; color: #666;
}
.empty { padding: 12px; color: #999; font-size: 13px;
}
.order-item { display: flex; gap: 10px; padding: 12px; border-top: 1px solid #f5f5f5;
}
.thumb { width: 96px; height: 72px; border-radius: 8px;
}
.order-body { flex: 1; display: flex; flex-direction: column; gap: 4px;
}
.row { display: flex; justify-content: space-between; align-items: center;
}
.name { font-weight: 600; color: #222;
}
.status { font-size: 12px; color: #999;
}
.s-done { color: #2ecc71;
}
.s-pending { color: #faad14;
}
.meta { font-size: 12px; color: #666;
}
.price { color: #222; font-weight: 600;
}
.income { color: #ff4d4f; font-weight: 600;
}
.time { font-size: 12px; color: #999;
}
.withdraw { margin: 12px;
}

