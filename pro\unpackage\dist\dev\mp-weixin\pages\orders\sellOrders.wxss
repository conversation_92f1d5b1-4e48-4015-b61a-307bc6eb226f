
.page { background: #f6f7fb; min-height: 100vh;
}
.item { display: flex; padding: 10px; background: #fff;
}
.thumb { width: 96px; height: 72px; border-radius: 8px;
}
.info { margin-left: 10px; flex: 1;
}
.row { display: flex; justify-content: space-between;
}
.name { font-weight: 600;
}
.status { font-size: 12px; color: #999;
}
.s-done { color: #2ecc71;
}
.s-talk { color: #faad14;
}
.s-review { color: #409eff;
}
.meta { font-size: 12px; color: #666; margin: 4px 0;
}
.price { color: #ff4d4f; font-weight: 600;
}

