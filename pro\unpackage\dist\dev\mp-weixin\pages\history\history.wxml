<view class="history-page data-v-24bfa630"><view class="header data-v-24bfa630"><text class="title data-v-24bfa630">浏览历史</text><text class="count data-v-24bfa630">{{$root.g0+"条记录"}}</text></view><block wx:if="{{$root.g1===0}}"><view class="empty-state data-v-24bfa630"><view class="empty-icon data-v-24bfa630">📖</view><text class="empty-text data-v-24bfa630">暂无浏览记录</text><text class="empty-desc data-v-24bfa630">去看看心仪的车辆吧</text><u-button class="empty-btn data-v-24bfa630" vue-id="7335a0d6-1" type="primary" size="small" data-event-opts="{{[['^click',[['goToBuy']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">去买车</u-button></view></block><block wx:else><view class="history-list data-v-24bfa630"><block wx:for="{{$root.l0}}" wx:for-item="car" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['history','',index]]]]]]]}}" class="history-item data-v-24bfa630" bindtap="__e"><image class="car-image data-v-24bfa630" src="{{car.$orig.image||'/static/car1.jpg'}}" mode="aspectFill"></image><view class="car-info data-v-24bfa630"><text class="car-name data-v-24bfa630">{{car.$orig.name}}</text><view class="car-meta data-v-24bfa630"><text class="meta-item data-v-24bfa630">{{car.$orig.year+"年"}}</text><text class="meta-item data-v-24bfa630">{{car.$orig.mileage+"万公里"}}</text><text class="meta-item data-v-24bfa630">{{car.$orig.city}}</text></view><view class="car-tags data-v-24bfa630"><block wx:for="{{car.$orig.tags}}" wx:for-item="tag" wx:for-index="tagIndex" wx:key="tagIndex"><text class="tag data-v-24bfa630">{{tag}}</text></block></view><view class="car-bottom data-v-24bfa630"><text class="car-price data-v-24bfa630">{{"￥"+car.m0}}</text><text class="view-time data-v-24bfa630">{{car.m1}}</text></view></view><view class="car-actions data-v-24bfa630"><view data-event-opts="{{[['tap',[['handleToggleFavorite',[index]]]]]}}" class="{{['action-btn','favorite-btn','data-v-24bfa630',(car.$orig.isFavorite)?'active':'']}}" catchtap="__e"><text class="action-icon data-v-24bfa630">{{car.$orig.isFavorite?'❤️':'🤍'}}</text><text class="action-text data-v-24bfa630">{{car.$orig.isFavorite?'已收藏':'收藏'}}</text></view><view data-event-opts="{{[['tap',[['removeHistory',[index]]]]]}}" class="action-btn delete-btn data-v-24bfa630" catchtap="__e"><text class="action-icon data-v-24bfa630">🗑️</text><text class="action-text data-v-24bfa630">删除</text></view></view></view></block></view></block><block wx:if="{{$root.g2>0}}"><view class="footer-actions data-v-24bfa630"><view data-event-opts="{{[['tap',[['clearAll',['$event']]]]]}}" class="clear-all-btn data-v-24bfa630" bindtap="__e"><text class="clear-btn-icon data-v-24bfa630">🗑️</text><text class="clear-btn-text data-v-24bfa630">清空历史</text></view></view></block></view>