<view class="favorites-page data-v-7df0fb94"><view class="header data-v-7df0fb94"><text class="title data-v-7df0fb94">我的收藏</text><text class="count data-v-7df0fb94">{{$root.g0+"辆车"}}</text></view><block wx:if="{{$root.g1===0}}"><view class="empty-state data-v-7df0fb94"><view class="empty-icon data-v-7df0fb94">💝</view><text class="empty-text data-v-7df0fb94">还没有收藏的车辆</text><text class="empty-desc data-v-7df0fb94">去看看心仪的车辆吧</text><u-button class="empty-btn data-v-7df0fb94" vue-id="5fe94590-1" type="primary" size="small" data-event-opts="{{[['^click',[['goToBuy']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">去买车</u-button></view></block><block wx:else><view class="car-list data-v-7df0fb94"><block wx:for="{{$root.l0}}" wx:for-item="car" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['favorites','id',car.$orig.id]]]]]]]}}" class="car-item data-v-7df0fb94" bindtap="__e"><image class="car-image data-v-7df0fb94" src="{{car.$orig.image||'/static/car1.jpg'}}" mode="aspectFill"></image><view class="car-info data-v-7df0fb94"><text class="car-name data-v-7df0fb94">{{car.$orig.name}}</text><view class="car-meta data-v-7df0fb94"><text class="meta-item data-v-7df0fb94">{{car.$orig.year+"年"}}</text><text class="meta-item data-v-7df0fb94">{{car.$orig.mileage+"万公里"}}</text><text class="meta-item data-v-7df0fb94">{{car.$orig.city}}</text></view><view class="car-tags data-v-7df0fb94"><block wx:for="{{car.$orig.tags}}" wx:for-item="tag" wx:for-index="tagIndex" wx:key="tagIndex"><text class="tag data-v-7df0fb94">{{tag}}</text></block></view><view class="car-bottom data-v-7df0fb94"><text class="car-price data-v-7df0fb94">{{"￥"+car.m0}}</text><text class="favorite-time data-v-7df0fb94">{{car.m1}}</text></view></view><view class="car-actions data-v-7df0fb94"><view data-event-opts="{{[['tap',[['removeFavorite',['$0'],[[['favorites','id',car.$orig.id,'id']]]]]]]}}" class="action-btn remove-btn data-v-7df0fb94" catchtap="__e"><text class="action-icon data-v-7df0fb94">🗑️</text><text class="action-text data-v-7df0fb94">删除</text></view></view></view></block></view></block><block wx:if="{{$root.g2>0}}"><view class="footer-actions data-v-7df0fb94"><view data-event-opts="{{[['tap',[['clearAll',['$event']]]]]}}" class="clear-all-btn data-v-7df0fb94" bindtap="__e"><text class="clear-btn-icon data-v-7df0fb94">🗑️</text><text class="clear-btn-text data-v-7df0fb94">清空收藏</text></view></view></block></view>