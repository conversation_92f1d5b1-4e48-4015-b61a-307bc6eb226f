<view class="profit-page"><view class="summary"><view class="summary-item"><text class="label">累计收益(元)</text><text class="value">{{$root.g0}}</text></view><view class="divider"></view><view class="summary-item"><text class="label">可提现(元)</text><text class="value highlight">{{$root.g1}}</text></view><view class="divider"></view><view class="summary-item"><text class="label">已提现(元)</text><text class="value">{{$root.g2}}</text></view></view><view class="card"><view class="card-title">快捷入口</view><u-cell-group vue-id="7352d300-1" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('7352d300-2')+','+('7352d300-1')}}" title="发布车辆" isLink="{{true}}" icon="edit-pen" data-event-opts="{{[['^click',[['goPublish']]]]}}" bind:click="__e" bind:__l="__l"></u-cell><u-cell vue-id="{{('7352d300-3')+','+('7352d300-1')}}" title="我的发布" isLink="{{true}}" icon="file-text" data-event-opts="{{[['^click',[['goMyListings']]]]}}" bind:click="__e" bind:__l="__l"></u-cell><u-cell vue-id="{{('7352d300-4')+','+('7352d300-1')}}" title="申请提现" isLink="{{true}}" icon="red-packet-fill" data-event-opts="{{[['^click',[['goWithdraw']]]]}}" bind:click="__e" bind:__l="__l"></u-cell></u-cell-group></view><view class="card"><view class="card-title">卖车订单</view><block wx:if="{{$root.g3===0}}"><view class="empty">暂无订单</view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view class="order-item"><image class="thumb" src="{{item.$orig.image}}" mode="aspectFill"></image><view class="order-body"><view class="row"><text class="name">{{item.$orig.model}}</text><text class="{{['status','s-'+item.$orig.status]}}">{{item.m0}}</text></view><view class="meta">{{item.$orig.year+"年 · "+item.$orig.mileage+"万公里 · "+item.$orig.city}}</view><view class="row"><text class="price">{{"成交价 ￥"+item.g4}}</text><text class="income">{{"收益 ￥"+item.g5}}</text></view><view class="time">{{"成交时间："+item.$orig.dealAt}}</view></view></view></block></view><view class="withdraw"><u-button vue-id="7352d300-5" type="primary" shape="circle" data-event-opts="{{[['^click',[['goWithdraw']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{"申请提现（可提 ￥"+$root.g6+"）"}}</u-button></view></view>