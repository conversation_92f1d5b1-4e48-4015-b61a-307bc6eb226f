@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.history-page.data-v-24bfa630 {
  background: #f6f7fb;
  min-height: 100vh;
}
.header.data-v-24bfa630 {
  background: #ffffff;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header .title.data-v-24bfa630 {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}
.header .count.data-v-24bfa630 {
  font-size: 14px;
  color: #595959;
}
.empty-state.data-v-24bfa630 {
  padding: 80px 20px;
  text-align: center;
}
.empty-state .empty-icon.data-v-24bfa630 {
  font-size: 64px;
  margin-bottom: 20px;
}
.empty-state .empty-text.data-v-24bfa630 {
  display: block;
  font-size: 18px;
  color: #262626;
  margin-bottom: 8px;
}
.empty-state .empty-desc.data-v-24bfa630 {
  display: block;
  font-size: 14px;
  color: #595959;
  margin-bottom: 30px;
}
.empty-state .empty-btn.data-v-24bfa630 {
  width: 120px;
}
.history-list.data-v-24bfa630 {
  padding: 12px;
}
.history-item.data-v-24bfa630 {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  gap: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.history-item.data-v-24bfa630:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.car-image.data-v-24bfa630 {
  width: 100px;
  height: 80px;
  border-radius: 8px;
  flex-shrink: 0;
}
.car-info.data-v-24bfa630 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.car-name.data-v-24bfa630 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1.3;
}
.car-meta.data-v-24bfa630 {
  display: flex;
  gap: 8px;
}
.car-meta .meta-item.data-v-24bfa630 {
  font-size: 12px;
  color: #595959;
}
.car-tags.data-v-24bfa630 {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}
.car-tags .tag.data-v-24bfa630 {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}
.car-bottom.data-v-24bfa630 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}
.car-price.data-v-24bfa630 {
  font-size: 16px;
  font-weight: 700;
  color: #ff4d4f;
}
.view-time.data-v-24bfa630 {
  font-size: 11px;
  color: #bfbfbf;
}
.car-actions.data-v-24bfa630 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  align-items: center;
}
.action-btn.data-v-24bfa630 {
  min-width: 60px;
  height: 32px;
  border-radius: 16px;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  padding: 4px 8px;
}
.action-btn.favorite-btn.active.data-v-24bfa630 {
  background: rgba(255, 77, 79, 0.1);
}
.action-btn.delete-btn.data-v-24bfa630 {
  background: rgba(255, 77, 79, 0.1);
}
.action-btn.data-v-24bfa630:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.action-icon.data-v-24bfa630 {
  font-size: 14px;
  line-height: 1;
}
.action-text.data-v-24bfa630 {
  font-size: 10px;
  color: #666;
  line-height: 1;
  margin-top: 2px;
}
.action-btn.favorite-btn.active .action-text.data-v-24bfa630 {
  color: #ff4d4f;
}
.action-btn.delete-btn .action-text.data-v-24bfa630 {
  color: #ff4d4f;
}
.footer-actions.data-v-24bfa630 {
  padding: 20px;
  text-align: center;
}
.clear-all-btn.data-v-24bfa630 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  background: rgba(255, 77, 79, 0.1);
  border: 1px solid #ff4d4f;
  border-radius: 20px;
  transition: all 0.3s ease;
}
.clear-all-btn.data-v-24bfa630:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: rgba(255, 77, 79, 0.2);
}
.clear-btn-icon.data-v-24bfa630 {
  font-size: 16px;
}
.clear-btn-text.data-v-24bfa630 {
  font-size: 14px;
  color: #ff4d4f;
  font-weight: 500;
}
