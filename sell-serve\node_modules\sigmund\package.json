{"name": "sigmund", "version": "1.0.1", "description": "Quick and dirty signatures for Objects.", "main": "sigmund.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js", "bench": "node bench.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/sigmund"}, "keywords": ["object", "signature", "key", "data", "psychoanalysis"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC"}