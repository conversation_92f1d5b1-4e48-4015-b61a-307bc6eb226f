{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "minimatch", "description": "a glob matcher in javascript", "version": "0.2.14", "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "main": "minimatch.js", "scripts": {"test": "tap test/*.js"}, "engines": {"node": "*"}, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "devDependencies": {"tap": ""}, "license": {"type": "MIT", "url": "http://github.com/isaacs/minimatch/raw/master/LICENSE"}}