<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心理健康关怀平台 - 系统架构图和用例图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .diagram {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #3498db;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .layer {
            background: white;
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .layer-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 5px;
        }
        .layer-desc {
            color: #666;
            font-size: 14px;
        }
        .arrow {
            text-align: center;
            color: #3498db;
            font-size: 24px;
            margin: 10px 0;
        }
        .modules {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .module {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .module-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .module-desc {
            color: #666;
            font-size: 13px;
        }
        .usecase-container {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .role {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #3498db;
            text-align: center;
            margin-bottom: 15px;
        }
        .role-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .usecase {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #3498db;
            font-size: 14px;
        }
        .usecase-counselor {
            border-left-color: #ffc107;
            background: #fff8e1;
        }
        .usecase-admin {
            border-left-color: #28a745;
            background: #e8f5e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>心理健康关怀平台</h1>
        <h2>系统总体架构图</h2>
        
        <div class="diagram">
            <div class="layer">
                <div class="layer-title">用户层</div>
                <div class="layer-desc">普通用户 | 心理辅导员 | 后台管理员</div>
            </div>
            
            <div class="arrow">↓</div>
            
            <div class="layer">
                <div class="layer-title">前端表现层</div>
                <div class="layer-desc">Vue.js框架 + 响应式页面设计 + 用户交互体验优化</div>
            </div>
            
            <div class="arrow">↓</div>
            
            <div class="layer">
                <div class="layer-title">业务逻辑层</div>
                <div class="layer-desc">Spring Boot框架 + 业务逻辑处理 + API接口</div>
            </div>
            
            <div class="arrow">↓</div>
            
            <div class="layer">
                <div class="layer-title">数据访问层</div>
                <div class="layer-desc">MyBatis/JPA + 数据库交互处理</div>
            </div>
            
            <div class="arrow">↓</div>
            
            <div class="layer">
                <div class="layer-title">数据库层</div>
                <div class="layer-desc">MySQL数据库 + 数据持久化存储</div>
            </div>
            
            <h3 style="text-align: center; color: #2c3e50; margin-top: 30px;">六大核心功能模块</h3>
            <div class="modules">
                <div class="module">
                    <div class="module-title">用户模块</div>
                    <div class="module-desc">• 注册登录<br>• 角色管理</div>
                </div>
                <div class="module">
                    <div class="module-title">心理测评模块</div>
                    <div class="module-desc">• 量表测试<br>• 报告生成</div>
                </div>
                <div class="module">
                    <div class="module-title">情绪日记模块</div>
                    <div class="module-desc">• 情绪记录<br>• 回顾分析</div>
                </div>
                <div class="module">
                    <div class="module-title">树洞互助模块</div>
                    <div class="module-desc">• 匿名倾诉<br>• 互相鼓励</div>
                </div>
                <div class="module">
                    <div class="module-title">心理知识模块</div>
                    <div class="module-desc">• 知识推送<br>• 案例分享</div>
                </div>
                <div class="module">
                    <div class="module-title">后台管理模块</div>
                    <div class="module-desc">• 数据管理<br>• 内容审核</div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px; padding: 15px; background: #f0f8ff; border-radius: 8px;">
                <strong>核心数据表：</strong>
                用户信息表 | 心理测评结果表 | 情绪日记表 | 社区互动表 | 心理知识表 | 系统日志表
            </div>
        </div>
        
        <h2>系统用例图</h2>
        
        <div class="diagram">
            <div class="usecase-container">
                <!-- 左侧：角色 -->
                <div>
                    <h4 style="text-align: center; color: #2c3e50;">系统角色</h4>
                    <div class="role">
                        <div class="role-title">普通用户</div>
                        <div style="font-size: 12px; color: #666;">寻求心理健康服务</div>
                    </div>
                    <div class="role" style="background: #fff8e1; border-color: #ffc107;">
                        <div class="role-title">心理辅导员</div>
                        <div style="font-size: 12px; color: #666;">提供专业指导服务</div>
                    </div>
                    <div class="role" style="background: #e8f5e8; border-color: #28a745;">
                        <div class="role-title">后台管理员</div>
                        <div style="font-size: 12px; color: #666;">管理平台运营</div>
                    </div>
                </div>
                
                <!-- 中间：用例 -->
                <div>
                    <h4 style="text-align: center; color: #2c3e50;">功能用例</h4>
                    
                    <div style="margin-bottom: 15px;">
                        <strong style="color: #3498db;">普通用户功能：</strong>
                        <div class="usecase">用户注册登录</div>
                        <div class="usecase">个人信息管理</div>
                        <div class="usecase">心理测评 → 查看测评报告</div>
                        <div class="usecase">情绪日记记录 → 情绪日记回顾</div>
                        <div class="usecase">匿名树洞倾诉</div>
                        <div class="usecase">互助留言鼓励</div>
                        <div class="usecase">浏览心理知识</div>
                        <div class="usecase">案例分享</div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <strong style="color: #ffc107;">心理辅导员功能：</strong>
                        <div class="usecase usecase-counselor">用户注册登录</div>
                        <div class="usecase usecase-counselor">个人信息管理</div>
                        <div class="usecase usecase-counselor">辅导员资料管理</div>
                        <div class="usecase usecase-counselor">查看用户测评</div>
                        <div class="usecase usecase-counselor">提供心理指导</div>
                        <div class="usecase usecase-counselor">浏览心理知识</div>
                        <div class="usecase usecase-counselor">案例分享</div>
                    </div>
                    
                    <div>
                        <strong style="color: #28a745;">后台管理员功能：</strong>
                        <div class="usecase usecase-admin">用户注册登录</div>
                        <div class="usecase usecase-admin">用户数据管理</div>
                        <div class="usecase usecase-admin">内容审核</div>
                        <div class="usecase usecase-admin">数据统计分析</div>
                    </div>
                </div>
                
                <!-- 右侧：关系说明 -->
                <div>
                    <h4 style="text-align: center; color: #2c3e50;">功能关系</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-size: 14px;">
                        <div style="margin-bottom: 10px;">
                            <strong>扩展关系：</strong><br>
                            心理测评 → 查看测评报告<br>
                            情绪日记记录 → 情绪日记回顾
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>包含关系：</strong><br>
                            所有功能都包含用户身份验证
                        </div>
                        <div>
                            <strong>继承关系：</strong><br>
                            所有角色都继承基本登录功能
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f4fd; border-radius: 8px; border: 1px solid #3498db;">
            <h3 style="color: #2c3e50; text-align: center;">技术架构特点</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                <div>✓ 前后端分离：Vue.js + Spring Boot</div>
                <div>✓ 响应式设计：适配多种设备</div>
                <div>✓ 模块化设计：六大功能模块</div>
                <div>✓ 数据安全：用户隐私保护</div>
                <div>✓ 角色权限：三级用户权限管理</div>
                <div>✓ 匿名功能：树洞倾诉支持</div>
            </div>
        </div>
    </div>
</body>
</html>
