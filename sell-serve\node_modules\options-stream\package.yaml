name: options-stream
main: index.js
version: 0.0.8
description: 'load options with stream mode, option file can be json/ini/yaml/ion file'
author:
  name: q3boy
  email: <EMAIL>
engines:
  node: >=0.6.0
homepage: https://github.com/q3boy/options-stream
repository:
  type: git
  url: git://github.com/q3boy/options-stream.git
bugs:
  url: https://github.com/q3boy/options-stream/issues
dependencies:
  ini: '*'
  yamljs: '0.1.6'
devDependencies:
  coffee-script: '*'
  mocha: '*'
  expect.js: '*'
  jscover: '*'
scripts:
  prepublish: make release
  postpublish: make clean
  test: make test-cov
