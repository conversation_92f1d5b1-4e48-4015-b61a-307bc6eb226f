/*
Copyright (c) 2010 <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is furnished
to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/
!function(){var a=function(a,b,c,d){this.rawMessage=a,this.parsedLine=void 0!==b?b:-1,this.snippet=void 0!==c?c:null,this.parsedFile=void 0!==d?d:null,this.updateRepr(),this.message=a};a.prototype={name:"YamlParseException",message:null,parsedFile:null,parsedLine:-1,snippet:null,rawMessage:null,isDefined:function(a){return void 0!=a&&null!=a},getSnippet:function(){return this.snippet},setSnippet:function(a){this.snippet=a,this.updateRepr()},getParsedFile:function(){return this.parsedFile},setParsedFile:function(a){this.parsedFile=a,this.updateRepr()},getParsedLine:function(){return this.parsedLine},setParsedLine:function(a){this.parsedLine=a,this.updateRepr()},updateRepr:function(){this.message=this.rawMessage;var a=!1;"."===this.message.charAt(this.message.length-1)&&(this.message=this.message.substring(0,this.message.length-1),a=!0),null!==this.parsedFile&&(this.message+=" in "+JSON.stringify(this.parsedFile)),this.parsedLine>=0&&(this.message+=" at line "+this.parsedLine),this.snippet&&(this.message+=' (near "'+this.snippet+'")'),a&&(this.message+=".")}};var b=!1,c=function(){};c.prototype={parseFile:function(b,d){if(null==d){var e=this.getFileContents(b),f=null;try{f=this.parse(e)}catch(g){throw g instanceof a&&g.setParsedFile(b),g}return f}this.getFileContents(b,function(a){d((new c).parse(a))})},parse:function(a){var b=new f;return b.parse(a)},dump:function(a,b,c){null==b&&(b=2);var d=new h;return c&&(d.numSpacesForIndentation=c),d.dump(a,b)},getXHR:function(){if(window.XMLHttpRequest)return new XMLHttpRequest;if(window.ActiveXObject)for(var a=["Msxml2.XMLHTTP.6.0","Msxml2.XMLHTTP.3.0","Msxml2.XMLHTTP","Microsoft.XMLHTTP"],b=0;4>b;b++)try{return new ActiveXObject(a[b])}catch(c){}return null},getFileContents:function(a,c){if(b){var d=require("fs");if(null==c){var e=d.readFileSync(a);return null==e?null:""+e}d.readFile(a,function(a,b){a?c(null):c(b)})}else{var f=this.getXHR();if(null==c)return f.open("GET",a,!1),f.send(null),200==f.status||0==f.status?f.responseText:null;f.onreadystatechange=function(){4==f.readyState&&(200==f.status||0==f.status?c(f.responseText):c(null))},f.open("GET",a,!0),f.send(null)}}};var d={stringify:function(a,b,d){return(new c).dump(a,b,d)},parse:function(a){return(new c).parse(a)},load:function(a,b){return(new c).parseFile(a,b)}};"undefined"!=typeof exports&&"undefined"!=typeof module&&module.exports&&(exports=module.exports=d,b=!0,function(){var a=function(a,b){a.exports=d.load(b)};void 0!==require.extensions&&(require.extensions[".yml"]=a,require.extensions[".yaml"]=a)}()),"undefined"!=typeof window&&(window.YAML=d);var e=function(){};e.prototype={i:null,parse:function(b){var c=null;if(b=this.trim(b),0==b.length)return"";switch(b.charAt(0)){case"[":c=this.parseSequence(b);break;case"{":c=this.parseMapping(b);break;default:c=this.parseScalar(b)}if(""!=b.substr(this.i+1).replace(/^\s*#.*$/,""))throw console.log("oups "+b.substr(this.i+1)),new a('Unexpected characters near "'+b.substr(this.i)+'".');return c},dump:function(a){if(void 0==a||null==a)return"null";if(a instanceof Date)return a.toISOString();if("object"==typeof a)return this.dumpObject(a);if("boolean"==typeof a)return a?"true":"false";if(/^\d+$/.test(a))return"string"==typeof a?"'"+a+"'":parseInt(a);if(this.isNumeric(a))return"string"==typeof a?"'"+a+"'":parseFloat(a);if("number"==typeof a)return 1/0==a?".Inf":a==-1/0?"-.Inf":isNaN(a)?".NAN":a;var b=new YamlEscaper;return b.requiresDoubleQuoting(a)?b.escapeWithDoubleQuotes(a):b.requiresSingleQuoting(a)?b.escapeWithSingleQuotes(a):""==a?'""':this.getTimestampRegex().test(a)?"'"+a+"'":this.inArray(a.toLowerCase(),["null","~","true","false"])?"'"+a+"'":a},dumpObject:function(a){var d,b=this.getKeys(a),c=null,e=b.length;if(a instanceof Array){for(c=[],d=0;e>d;d++)c.push(this.dump(a[b[d]]));return"["+c.join(", ")+"]"}for(c=[],d=0;e>d;d++)c.push(this.dump(b[d])+": "+this.dump(a[b[d]]));return"{ "+c.join(", ")+" }"},parseScalar:function(b,c,d,e,f){void 0==c&&(c=null),void 0==d&&(d=['"',"'"]),void 0==e&&(e=0),void 0==f&&(f=!0);var g=null,h=null,i=null;if(this.inArray(b[e],d)){if(g=this.parseQuotedScalar(b,e),e=this.i,null!==c){var j=b.substr(e).replace(/^\s+/,"");if(!this.inArray(j.charAt(0),c))throw new a("Unexpected characters ("+b.substr(e)+").")}}else{if(c){if(!(i=new RegExp("^(.+?)("+c.join("|")+")").exec((b+"").substring(e))))throw new a("Malformed inline YAML string ("+b+").");g=i[1],e+=g.length}else g=(b+"").substring(e),e+=g.length,h=g.indexOf(" #"),-1!=h&&(g=g.substr(0,h).replace(/\s+$/g,""));g=f?this.evaluateScalar(g):g}return this.i=e,g},parseQuotedScalar:function(b,c){var d=null;if(!(d=new RegExp("^"+e.REGEX_QUOTED_STRING).exec((b+"").substring(c))))throw new a("Malformed inline YAML string ("+(b+"").substring(c)+").");var f=d[0].substr(1,d[0].length-2),h=new g;return f='"'==(b+"").charAt(c)?h.unescapeDoubleQuotedString(f):h.unescapeSingleQuotedString(f),c+=d[0].length,this.i=c,f},parseSequence:function(b,c){void 0==c&&(c=0);var d=[],e=b.length;for(c+=1;e>c;){switch(b.charAt(c)){case"[":d.push(this.parseSequence(b,c)),c=this.i;break;case"{":d.push(this.parseMapping(b,c)),c=this.i;break;case"]":return this.i=c,d;case",":case" ":break;default:var f=this.inArray(b.charAt(c),['"',"'"]),g=this.parseScalar(b,[",","]"],['"',"'"],c);if(c=this.i,!f&&-1!=(g+"").indexOf(": "))try{g=this.parseMapping("{"+g+"}")}catch(h){if(!(h instanceof a))throw h}d.push(g),c--}c++}throw new a('Malformed inline YAML string "'+b+'"')},parseMapping:function(b,c){void 0==c&&(c=0);var d={},e=b.length;c+=1;for(var f=!1,g=!1;e>c;){switch(g=!1,b.charAt(c)){case" ":case",":c++,g=!0;break;case"}":return this.i=c,d}if(!g){var h=this.parseScalar(b,[":"," "],['"',"'"],c,!1);for(c=this.i,f=!1;e>c;){switch(b.charAt(c)){case"[":d[h]=this.parseSequence(b,c),c=this.i,f=!0;break;case"{":d[h]=this.parseMapping(b,c),c=this.i,f=!0;break;case":":case" ":break;default:d[h]=this.parseScalar(b,[",","}"],['"',"'"],c),c=this.i,f=!0,c--}if(++c,f){g=!0;break}}}}throw new a('Malformed inline YAML string "'+b+'"')},evaluateScalar:function(a){a=this.trim(a);var b=null,c=null;return"null"==a.toLowerCase()||""==a||"~"==a?null:0==(a+"").indexOf("!str ")?(""+a).substring(5):0==(a+"").indexOf("! ")?parseInt(this.parseScalar((a+"").substr(2))):/^\d+$/.test(a)?(b=a,c=parseInt(a),"0"==a.charAt(0)?this.octdec(a):""+b==""+c?c:b):"true"==(a+"").toLowerCase()?!0:"false"==(a+"").toLowerCase()?!1:this.isNumeric(a)?"0x"==(a+"").substr(0,2)?this.hexdec(a):parseFloat(a):".inf"==a.toLowerCase()?1/0:".nan"==a.toLowerCase()?0/0:"-.inf"==a.toLowerCase()?-1/0:/^(-|\+)?[0-9,]+(\.[0-9]+)?$/.test(a)?parseFloat(a.split(",").join("")):this.getTimestampRegex().test(a)?new Date(this.strtotime(a)):""+a},getTimestampRegex:function(){return new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:(?:[Tt]|[ 	]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:.([0-9]*))?(?:[ 	]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?)?$","gi")},trim:function(a){return(a+"").replace(/^\s+/,"").replace(/\s+$/,"")},isNumeric:function(a){return a-0==a&&a.length>0&&""!=a.replace(/\s+/g,"")},inArray:function(a,b){var c,d=b.length;for(c=0;d>c;c++)if(a==b[c])return!0;return!1},getKeys:function(a){var b=[];for(var c in a)a.hasOwnProperty(c)&&b.push(c);return b},octdec:function(a){return parseInt((a+"").replace(/[^0-7]/gi,""),8)},hexdec:function(a){return a=this.trim(a),"0x"==(a+"").substr(0,2)&&(a=(a+"").substring(2)),parseInt((a+"").replace(/[^a-f0-9]/gi,""),16)},strtotime:function(a,b){var c,d,e,f,g="";if(a=(a+"").replace(/\s{2,}|^\s|\s$/g," ").replace(/[\t\r\n]/g,""),"now"===a)return null===b||isNaN(b)?(new Date).getTime()||0:b||0;if(!isNaN(g=Date.parse(a)))return g||0;b=b?new Date(b):new Date,a=a.toLowerCase();var h={day:{sun:0,mon:1,tue:2,wed:3,thu:4,fri:5,sat:6},mon:["jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec"]},i=function(a){var c=a[2]&&"ago"===a[2],d=(d="last"===a[0]?-1:1)*(c?-1:1);switch(a[0]){case"last":case"next":switch(a[1].substring(0,3)){case"yea":b.setFullYear(b.getFullYear()+d);break;case"wee":b.setDate(b.getDate()+7*d);break;case"day":b.setDate(b.getDate()+d);break;case"hou":b.setHours(b.getHours()+d);break;case"min":b.setMinutes(b.getMinutes()+d);break;case"sec":b.setSeconds(b.getSeconds()+d);break;case"mon":if("month"===a[1]){b.setMonth(b.getMonth()+d);break}default:var e=h.day[a[1].substring(0,3)];if("undefined"!=typeof e){var f=e-b.getDay();0===f?f=7*d:f>0?"last"===a[0]&&(f-=7):"next"===a[0]&&(f+=7),b.setDate(b.getDate()+f),b.setHours(0,0,0,0)}}break;default:if(!/\d+/.test(a[0]))return!1;switch(d*=parseInt(a[0],10),a[1].substring(0,3)){case"yea":b.setFullYear(b.getFullYear()+d);break;case"mon":b.setMonth(b.getMonth()+d);break;case"wee":b.setDate(b.getDate()+7*d);break;case"day":b.setDate(b.getDate()+d);break;case"hou":b.setHours(b.getHours()+d);break;case"min":b.setMinutes(b.getMinutes()+d);break;case"sec":b.setSeconds(b.getSeconds()+d)}}return!0};if(e=a.match(/^(\d{2,4}-\d{2}-\d{2})(?:\s(\d{1,2}:\d{2}(:\d{2})?)?(?:\.(\d+))?)?$/),null!==e)return e[2]?e[3]||(e[2]+=":00"):e[2]="00:00:00",f=e[1].split(/-/g),f[1]=h.mon[f[1]-1]||f[1],f[0]=+f[0],f[0]=f[0]>=0&&f[0]<=69?"20"+(f[0]<10?"0"+f[0]:f[0]+""):f[0]>=70&&f[0]<=99?"19"+f[0]:f[0]+"",parseInt(this.strtotime(f[2]+" "+f[1]+" "+f[0]+" "+e[2])+(e[4]?e[4]:""),10);var j="([+-]?\\d+\\s(years?|months?|weeks?|days?|hours?|min|minutes?|sec|seconds?|sun\\.?|sunday|mon\\.?|monday|tue\\.?|tuesday|wed\\.?|wednesday|thu\\.?|thursday|fri\\.?|friday|sat\\.?|saturday)|(last|next)\\s(years?|months?|weeks?|days?|hours?|min|minutes?|sec|seconds?|sun\\.?|sunday|mon\\.?|monday|tue\\.?|tuesday|wed\\.?|wednesday|thu\\.?|thursday|fri\\.?|friday|sat\\.?|saturday))(\\sago)?";if(e=a.match(new RegExp(j,"gi")),null===e)return!1;for(c=0,d=e.length;d>c;c++)if(!i(e[c].split(" ")))return!1;return b.getTime()||0}},e.REGEX_QUOTED_STRING="(?:\"(?:[^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\"|'(?:[^']*(?:''[^']*)*)')";var f=function(a){this.offset=void 0!==a?a:0};f.prototype={offset:0,lines:[],currentLineNb:-1,currentLine:"",refs:{},parse:function(b){this.currentLineNb=-1,this.currentLine="",this.lines=this.cleanup(b).split("\n");for(var c=null,d=null;this.moveToNextLine();)if(!this.isCurrentLineEmpty()){if("	"==this.currentLine.charAt(0))throw new a("A YAML file cannot contain tabs as indentation.",this.getRealCurrentLineNb()+1,this.currentLine);var g=!1,h=!1,i=!1,j=null,k=null,l=null,m=null,n=null,o=null,p=null,q=null,r=null;if(j=/^\-((\s+)(.+?))?\s*$/.exec(this.currentLine)){if(d&&"mapping"==d)throw new a("You cannot define a sequence item when in a mapping",this.getRealCurrentLineNb()+1,this.currentLine);d="sequence",this.isDefined(c)||(c=[]),j={leadspaces:j[2],value:j[3]},this.isDefined(j.value)&&(k=/^&([^ ]+) *(.*)/.exec(j.value))&&(k={ref:k[1],value:k[2]},g=k.ref,j.value=k.value),this.isDefined(j.value)&&""!=this.trim(j.value)&&"#"!=j.value.replace(/^ +/,"").charAt(0)?this.isDefined(j.leadspaces)&&" "==j.leadspaces&&(k=new RegExp("^("+e.REGEX_QUOTED_STRING+"|[^ '\"{[].*?) *:(\\s+(.+?))?\\s*$").exec(j.value))?(k={key:k[1],value:k[3]},l=this.getRealCurrentLineNb(),m=new f(l),m.refs=this.refs,n=j.value,this.isNextLineIndented()||(n+="\n"+this.getNextEmbedBlock(this.getCurrentLineIndentation()+2)),c.push(m.parse(n)),this.refs=m.refs):c.push(this.parseValue(j.value)):(l=this.getRealCurrentLineNb()+1,m=new f(l),m.refs=this.refs,c.push(m.parse(this.getNextEmbedBlock())),this.refs=m.refs)}else{if(!(j=new RegExp("^("+e.REGEX_QUOTED_STRING+"|[^ '\"[{].*?) *:(\\s+(.+?))?\\s*$").exec(this.currentLine))){if(2==this.lines.length&&this.isEmpty(this.lines[1])){try{b=(new e).parse(this.lines[0])}catch(s){throw s instanceof a&&(s.setParsedLine(this.getRealCurrentLineNb()+1),s.setSnippet(this.currentLine)),s}if(this.isObject(b)){var w=b[0];if("string"==typeof b&&"*"==w.charAt(0)){c=[],q=b.length;for(var u=0;q>u;u++)c.push(this.refs[b[u].substr(1)]);b=c}}return b}throw new a("Unable to parse.",this.getRealCurrentLineNb()+1,this.currentLine)}if(this.isDefined(c)||(c={}),d&&"sequence"==d)throw new a("You cannot define a mapping item when in a sequence",this.getRealCurrentLineNb()+1,this.currentLine);d="mapping",j={key:j[1],value:j[3]};try{o=(new e).parseScalar(j.key)}catch(s){throw s instanceof a&&(s.setParsedLine(this.getRealCurrentLineNb()+1),s.setSnippet(this.currentLine)),s}if("<<"==o)if(this.isDefined(j.value)&&"*"==(j.value+"").charAt(0)){if(h=j.value.substr(1),void 0==this.refs[h])throw new a('Reference "'+b+'" does not exist',this.getRealCurrentLineNb()+1,this.currentLine)}else{b=this.isDefined(j.value)&&""!=j.value?j.value:this.getNextEmbedBlock(),l=this.getRealCurrentLineNb()+1,m=new f(l),m.refs=this.refs,p=m.parse(b),this.refs=m.refs;var t=[];if(!this.isObject(p))throw new a("YAML merge keys used with a scalar value instead of an array",this.getRealCurrentLineNb()+1,this.currentLine);if(this.isDefined(p[0])){r=this.reverseArray(p),q=r.length;for(var u=0;q>u;u++){if(r[u],!this.isObject(r[u]))throw new a("Merge items must be arrays",this.getRealCurrentLineNb()+1,this.currentLine);t=this.mergeObject(r[u],t)}}else t=this.mergeObject(t,p);i=t}else this.isDefined(j.value)&&(k=/^&([^ ]+) *(.*)/.exec(j.value))&&(k={ref:k[1],value:k[2]},g=k.ref,j.value=k.value);i?c=i:this.isDefined(j.value)&&""!=this.trim(j.value)&&"#"!=this.trim(j.value).charAt(0)?h?c=this.refs[h]:c[o]=this.parseValue(j.value):this.isNextLineIndented()&&!this.isNextLineUnIndentedCollection()?c[o]=null:(l=this.getRealCurrentLineNb()+1,m=new f(l),m.refs=this.refs,c[o]=m.parse(this.getNextEmbedBlock()),this.refs=m.refs)}if(g)if(c instanceof Array)this.refs[g]=c[c.length-1];else{var x=null;for(var y in c)c.hasOwnProperty(y)&&(x=y);this.refs[g]=c[y]}}return this.isEmpty(c)?null:c},getRealCurrentLineNb:function(){return this.currentLineNb+this.offset},getCurrentLineIndentation:function(){return this.currentLine.length-this.currentLine.replace(/^ +/g,"").length},getNextEmbedBlock:function(b){this.moveToNextLine();var c=null,d=null;if(this.isDefined(b))c=b;else{c=this.getCurrentLineIndentation();var e=this.isStringUnIndentedCollectionItem(this.currentLine);if(!this.isCurrentLineEmpty()&&0==c&&!e)throw new a("Indentation problem A",this.getRealCurrentLineNb()+1,this.currentLine)}var f=[this.currentLine.substr(c)],g=this.isStringUnIndentedCollectionItem(this.currentLine),h=-1;for(g===!0&&(h=1+/^\-((\s+)(.+?))?\s*$/.exec(this.currentLine)[2].length);this.moveToNextLine();){if(g&&!this.isStringUnIndentedCollectionItem(this.currentLine)&&this.getCurrentLineIndentation()!=h){this.moveToPreviousLine();break}if(this.isCurrentLineEmpty())this.isCurrentLineBlank()&&f.push(this.currentLine.substr(c));else{d=this.getCurrentLineIndentation();var i;if(i=/^( *)$/.exec(this.currentLine))f.push(i[1]);else{if(!(d>=c)){if(0==d){this.moveToPreviousLine();break}throw new a("Indentation problem B",this.getRealCurrentLineNb()+1,this.currentLine)}f.push(this.currentLine.substr(c))}}}return f.join("\n")},moveToNextLine:function(){return this.currentLineNb>=this.lines.length-1?!1:(this.currentLineNb++,this.currentLine=this.lines[this.currentLineNb],!0)},moveToPreviousLine:function(){this.currentLineNb--,this.currentLine=this.lines[this.currentLineNb]},parseValue:function(b){if("*"==(b+"").charAt(0)){if(b="#"==this.trim(b).charAt(0)?(b+"").substr(1,b.indexOf("#")-2):(b+"").substr(1),void 0==this.refs[b])throw new a('Reference "'+b+'" does not exist',this.getRealCurrentLineNb()+1,this.currentLine);return this.refs[b]}var c=null;if(c=/^(\||>)(\+|\-|\d+|\+\d+|\-\d+|\d+\+|\d+\-)?( +#.*)?$/.exec(b)){c={separator:c[1],modifiers:c[2],comments:c[3]};var d=this.isDefined(c.modifiers)?c.modifiers:"";return this.parseFoldedScalar(c.separator,d.replace(/\d+/g,""),Math.abs(parseInt(d)))}try{return(new e).parse(b)}catch(f){throw f instanceof a&&(f.setParsedLine(this.getRealCurrentLineNb()+1),f.setSnippet(this.currentLine)),f}},parseFoldedScalar:function(a,b,c){void 0==b&&(b=""),void 0==c&&(c=0),a="|"==a?"\n":" ";for(var d="",e=null,f=this.moveToNextLine();f&&this.isCurrentLineBlank();)d+="\n",f=this.moveToNextLine();if(!f)return"";var g=null;if(!(g=new RegExp("^("+(c?this.strRepeat(" ",c):" +")+")(.*)$").exec(this.currentLine)))return this.moveToPreviousLine(),"";g={indent:g[1],text:g[2]};var h=g.indent,i=0;for(d+=g.text+a;this.currentLineNb+1<this.lines.length;)if(this.moveToNextLine(),g=new RegExp("^( {"+h.length+",})(.+)$").exec(this.currentLine))g={indent:g[1],text:g[2]}," "==a&&i!=g.indent&&(d=d.substr(0,d.length-1)+"\n"),i=g.indent,e=g.indent.length-h.length,d+=this.strRepeat(" ",e)+g.text+(0!=e?"\n":a);else{if(!(g=/^( *)$/.exec(this.currentLine))){this.moveToPreviousLine();break}d+=g[1].replace(new RegExp("^ {1,"+h.length+"}","g"),"")+"\n"}switch(" "==a&&(d=d.replace(/ (\n*)$/g,"\n$1")),b){case"":d=d.replace(/\n+$/g,"\n");break;case"+":break;case"-":d=d.replace(/\n+$/g,"")}return d},isNextLineIndented:function(){for(var a=this.getCurrentLineIndentation(),b=this.moveToNextLine();b&&this.isCurrentLineEmpty();)b=this.moveToNextLine();if(0==b)return!1;var c=!1;return this.getCurrentLineIndentation()<=a&&(c=!0),this.moveToPreviousLine(),c},isCurrentLineEmpty:function(){return this.isCurrentLineBlank()||this.isCurrentLineComment()},isCurrentLineBlank:function(){return""==this.trim(this.currentLine)},isCurrentLineComment:function(){var a=this.currentLine.replace(/^ +/g,"");return"#"==a.charAt(0)},cleanup:function(a){a=a.split("\r\n").join("\n").split("\r").join("\n"),/\n$/.test(a)||(a+="\n");for(var b=0,c=/^\%YAML[: ][\d\.]+.*\n/;c.test(a);)a=a.replace(c,""),b++;if(this.offset+=b,c=/^(#.*?\n)+/,c.test(a)){var d=a.replace(c,"");this.offset+=this.subStrCount(a,"\n")-this.subStrCount(d,"\n"),a=d}return c=/^\-\-\-.*?\n/,c.test(a)&&(d=a.replace(c,""),this.offset+=this.subStrCount(a,"\n")-this.subStrCount(d,"\n"),a=d,a=a.replace(/\.\.\.\s*$/g,"")),a},isNextLineUnIndentedCollection:function(){for(var a=this.getCurrentLineIndentation(),b=this.moveToNextLine();b&&this.isCurrentLineEmpty();)b=this.moveToNextLine();if(!1===b)return!1;var c=!1;return this.getCurrentLineIndentation()==a&&this.isStringUnIndentedCollectionItem(this.currentLine)&&(c=!0),this.moveToPreviousLine(),c},isStringUnIndentedCollectionItem:function(){return 0===this.currentLine.indexOf("- ")},isObject:function(a){return"object"==typeof a&&this.isDefined(a)},isEmpty:function(a){return void 0==a||null==a||""==a||0==a||"0"==a||0==a},isDefined:function(a){return void 0!=a&&null!=a},reverseArray:function(a){for(var b=[],c=a.length,d=c-1;d>=0;d--)b.push(a[d]);return b},merge:function(a,b){var d,c={};for(d in a)a.hasOwnProperty(d)&&(/^\d+$/.test(d)?c.push(a):c[d]=a[d]);for(d in b)b.hasOwnProperty(d)&&(/^\d+$/.test(d)?c.push(b):c[d]=b[d]);return c},strRepeat:function(a,b){var c,d="";for(c=0;b>c;c++)d+=a;return d},subStrCount:function(a,b,c,d){var e=0;a=""+a,b=""+b,void 0!=c&&(a=a.substr(c)),void 0!=d&&(a=a.substr(0,d));for(var f=a.length,g=b.length,h=0;f>h;h++)b==a.substr(h,g)&&e++,h+=g-1;return e},trim:function(a){return(a+"").replace(/^ +/,"").replace(/ +$/,"")}},YamlEscaper=function(){},YamlEscaper.prototype={requiresDoubleQuoting:function(a){return new RegExp(YamlEscaper.REGEX_CHARACTER_TO_ESCAPE).test(a)},escapeWithDoubleQuotes:function(a){a+="";for(var b=YamlEscaper.escapees.length,c=YamlEscaper.escaped.length,d=YamlEscaper.escaped,e=0;b>e;++e)e>=c&&d.push("");var f="";return f=a.replace(new RegExp(YamlEscaper.escapees.join("|"),"g"),function(a){for(var c=0;b>c;++c)if(a==YamlEscaper.escapees[c])return d[c]}),'"'+f+'"'},requiresSingleQuoting:function(a){return/[\s'":{}[\],&*#?]|^[-?|<>=!%@`]/.test(a)},escapeWithSingleQuotes:function(a){return"'"+a.replace(/'/g,"''")+"'"}},YamlEscaper.REGEX_CHARACTER_TO_ESCAPE="[\\x00-\\x1f]|\xc2\x85|\xc2\xa0|\xe2\x80\xa8|\xe2\x80\xa9",YamlEscaper.escapees=["\\\\",'\\"','"',"\0","","","","","","","","\b","	","\n","","\f","\r","","","","","","","","","","","","","","","","","","","\xc2\x85","\xc2\xa0","\xe2\x80\xa8","\xe2\x80\xa9"],YamlEscaper.escaped=['\\"',"\\\\",'\\"',"\\0","\\x01","\\x02","\\x03","\\x04","\\x05","\\x06","\\a","\\b","\\t","\\n","\\v","\\f","\\r","\\x0e","\\x0f","\\x10","\\x11","\\x12","\\x13","\\x14","\\x15","\\x16","\\x17","\\x18","\\x19","\\x1a","\\e","\\x1c","\\x1d","\\x1e","\\x1f","\\N","\\_","\\L","\\P"];var g=function(){};g.prototype={unescapeSingleQuotedString:function(a){return a.replace(/''/g,"'")},unescapeDoubleQuotedString:function(a){var b=function(a){return(new g).unescapeCharacter(a)};return a.replace(new RegExp(g.REGEX_ESCAPED_CHARACTER,"g"),b)},unescapeCharacter:function(a){switch(a.charAt(1)){case"0":return String.fromCharCode(0);case"a":return String.fromCharCode(7);case"b":return String.fromCharCode(8);case"t":return"	";case"	":return"	";case"n":return"\n";case"v":return String.fromCharCode(11);case"f":return String.fromCharCode(12);case"r":return String.fromCharCode(13);case"e":return"";case" ":return" ";case'"':return'"';case"/":return"/";case"\\":return"\\";case"N":return"\0\x85";case"_":return"\0\xa0";case"L":return" (";case"P":return" )";case"x":return this.pack("n",(new e).hexdec(a.substr(2,2)));case"u":return this.pack("n",(new e).hexdec(a.substr(2,4)));case"U":return this.pack("N",(new e).hexdec(a.substr(2,8)))}},pack:function(a){for(var h,i,b=0,c=1,d="",f=0;b<a.length;){for(h=a.charAt(b),i="",b++;b<a.length&&null!==a.charAt(b).match(/[\d\*]/);)i+=a.charAt(b),b++;switch(""===i&&(i="1"),h){case"n":if("*"===i&&(i=arguments.length-c),i>arguments.length-c)throw new Error("Warning:  pack() Type "+h+": too few arguments");for(f=0;i>f;f++)d+=String.fromCharCode(255&arguments[c]>>8),d+=String.fromCharCode(255&arguments[c]),c++;break;case"N":if("*"===i&&(i=arguments.length-c),i>arguments.length-c)throw new Error("Warning:  pack() Type "+h+": too few arguments");for(f=0;i>f;f++)d+=String.fromCharCode(255&arguments[c]>>24),d+=String.fromCharCode(255&arguments[c]>>16),d+=String.fromCharCode(255&arguments[c]>>8),d+=String.fromCharCode(255&arguments[c]),c++;break;default:throw new Error("Warning:  pack() Type "+h+": unknown format code")}}if(c<arguments.length)throw new Error("Warning: pack(): "+(arguments.length-c)+" arguments unused");return d}},g.REGEX_ESCAPED_CHARACTER='\\\\([0abt	nvfre "\\/\\\\N_LP]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})';var h=function(){};h.prototype={dump:function(a,b,c){null==b&&(b=0),null==c&&(c=0);var g,d="",f=c?this.strRepeat(" ",c):"";if(this.numSpacesForIndentation||(this.numSpacesForIndentation=2),0>=b||!this.isObject(a)||this.isEmpty(a))g=new e,d+=f+g.dump(a);else{var i,h=!this.arrayEquals(this.getKeys(a),this.range(0,a.length-1));for(var j in a)a.hasOwnProperty(j)&&(i=0>=b-1||!this.isObject(a[j])||this.isEmpty(a[j]),h&&(g=new e),d+=f+""+(h?g.dump(j)+":":"-")+(i?" ":"\n")+this.dump(a[j],b-1,i?0:c+this.numSpacesForIndentation)+(i?"\n":""))}return d},strRepeat:function(a,b){var c,d="";for(c=0;b>c;c++)d+=a;return d},isObject:function(a){return this.isDefined(a)&&"object"==typeof a},isEmpty:function(a){var b=void 0==a||null==a||""==a||0==a||"0"==a||0==a;if(!(b||"object"!=typeof a||a instanceof Array)){var c=0;for(var d in a)a.hasOwnProperty(d)&&c++;b=!c}return b},isDefined:function(a){return void 0!=a&&null!=a},getKeys:function(a){var b=[];for(var c in a)a.hasOwnProperty(c)&&b.push(c);return b},range:function(a,b){if(a>b)return[];for(var c=[],d=a;b>=d;d++)c.push(d);return c},arrayEquals:function(a,b){if(a.length!=b.length)return!1;for(var c=a.length,d=0;c>d;d++)if(a[d]!=b[d])return!1;return!0}}}();