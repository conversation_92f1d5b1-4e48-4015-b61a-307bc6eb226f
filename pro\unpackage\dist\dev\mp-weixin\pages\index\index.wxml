<view class="index-page"><view class="header-section"><view class="header-decoration"><view class="decoration-circle circle-1"></view><view class="decoration-circle circle-2"></view><view class="decoration-circle circle-3"></view><view class="decoration-wave"></view></view><view class="status-bar"><view class="brand-info"><text class="brand-name">铜墙铁壁</text><text class="brand-slogan">专业二手车平台</text></view><view data-event-opts="{{[['tap',[['goToProfile',['$event']]]]]}}" class="user-avatar" bindtap="__e"><image class="avatar-img" src="/static/牛油果姑娘.png" mode="aspectFill"></image></view></view><view class="search-container"><view class="search-wrapper"><u-search bind:input="__e" vue-id="8dd740cc-1" placeholder="搜索车名/品牌，例如 宝马3系" showAction="{{false}}" bgColor="rgba(255,255,255,0.95)" borderColor="transparent" searchIconColor="#1890ff" height="44" shape="round" value="{{keyword}}" data-event-opts="{{[['^input',[['__set_model',['','keyword','$event',[]]]]]]}}" bind:__l="__l"></u-search><view data-event-opts="{{[['tap',[['showFilterPopup',['$event']]]]]}}" class="search-filter" bindtap="__e"><text class="filter-icon">🔍</text></view></view><view class="location-weather"><picker mode="selector" range="{{hotCities}}" data-event-opts="{{[['change',[['onCityPick',['$event']]]]]}}" bindchange="__e"><view class="location-item"><view class="location-icon-wrapper"><text class="location-icon">📍</text></view><text class="location-text">{{city}}</text><text class="dropdown-icon">▼</text></view></picker><view class="weather-info"><view class="weather-icon">🌤️</view><text class="weather-text">{{weather+" "+temperature+"℃"}}</text></view></view><view class="quick-search-tags"><block wx:for="{{quickSearchTags}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><view data-event-opts="{{[['tap',[['quickSearch',['$0'],[[['quickSearchTags','',__i0__]]]]]]]}}" class="search-tag" bindtap="__e"><text class="tag-text">{{tag}}</text></view></block></view></view></view><view class="banner-section"><view class="banner-header"><text class="banner-title">热门车型</text><text class="banner-subtitle">精选优质二手车</text></view><u-swiper vue-id="8dd740cc-2" list="{{banner}}" height="240" indicator="{{true}}" indicatorMode="dot" circular="{{true}}" autoplay="{{true}}" interval="4000" radius="16" bind:__l="__l"></u-swiper></view><view class="quick-actions"><view class="section-title"><text class="title-text">快捷服务</text><text class="title-subtitle">一站式二手车服务</text></view><view class="actions-grid"><view data-event-opts="{{[['tap',[['goBuy',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon-wrapper buy-icon"><image class="action-icon" src="/static/买车.png"></image></view><text class="action-text">买车</text><text class="action-desc">精选好车</text></view><view data-event-opts="{{[['tap',[['goSell',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon-wrapper sell-icon"><image class="action-icon" src="/static/卖车.png"></image></view><text class="action-text">卖车估价</text><text class="action-desc">智能评估</text></view><view data-event-opts="{{[['tap',[['goProfit',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon-wrapper profit-icon"><image class="action-icon" src="/static/收益.png"></image></view><text class="action-text">收益</text><text class="action-desc">收益管理</text></view></view></view><view class="promo-section"><view class="promo-card"><view class="promo-content"><view class="promo-title">严选二手车 · 质保可查</view><view class="promo-subtitle">支持分期 · 全国可提</view><view class="promo-features"><view class="feature-item"><text class="feature-icon">✓</text><text class="feature-text">质量保证</text></view><view class="feature-item"><text class="feature-icon">✓</text><text class="feature-text">全国联保</text></view></view></view><image class="promo-image" src="/static/牛油果姑娘.png"></image></view></view><view class="section"><view class="section-header"><view class="section-title"><text class="title-text">热门推荐</text><text class="title-subtitle">精选优质车源</text></view><view data-event-opts="{{[['tap',[['goBuy',['$event']]]]]}}" class="section-more" bindtap="__e"><text class="more-text">查看更多</text><text class="more-icon">→</text></view></view><scroll-view class="hot-scroll" scroll-x="true" show-scrollbar="false"><view class="hot-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="i" wx:key="i"><view data-event-opts="{{[['tap',[['goInfo',['$0'],[[['hot','',i]]]]]]]}}" class="hot-item" bindtap="__e"><view class="hot-image-wrapper"><image class="hot-image" src="{{item.$orig.img}}" mode="aspectFill"></image><view class="hot-badge">热销</view></view><view class="hot-content"><view class="hot-name">{{item.$orig.name}}</view><view class="hot-meta">{{item.$orig.year+"年 · "+item.$orig.mileage+"万公里"}}</view><view class="hot-price">{{"￥"+item.g0}}</view></view></view></block></view></scroll-view></view><view class="section"><view class="section-header"><view class="section-title"><text class="title-text">猜你喜欢</text><text class="title-subtitle">个性化推荐</text></view></view><view class="like-list"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="i" wx:key="i"><view data-event-opts="{{[['tap',[['goInfo',['$0'],[[['like','',i]]]]]]]}}" class="like-item" bindtap="__e"><view class="like-image-wrapper"><image class="like-image" src="{{item.$orig.img}}" mode="aspectFill"></image></view><view class="like-content"><view class="like-name">{{item.$orig.name}}</view><view class="like-tags"><view class="modern-tag">{{item.$orig.year+"年"}}</view><view class="modern-tag">{{item.$orig.mileage+"万公里"}}</view></view><view class="like-location">{{"📍 "+item.$orig.city}}</view><view class="like-price-row"><text class="like-price">{{"￥"+item.g1}}</text><text class="like-original-price">{{"￥"+item.g2}}</text></view></view></view></block></view></view></view>