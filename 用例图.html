<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心理健康关怀平台 - 用例图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .diagram-container {
            width: 900px;
            height: 650px;
            border: 2px solid #333;
            background: white;
            position: relative;
            padding: 20px;
            box-sizing: border-box;
        }
        .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }
        
        /* 角色样式 */
        .actor {
            position: absolute;
            width: 80px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
        }
        .actor-figure {
            width: 30px;
            height: 60px;
            margin: 0 auto 8px;
            position: relative;
        }
        .actor-head {
            width: 20px;
            height: 20px;
            border: 2px solid #333;
            border-radius: 50%;
            margin: 0 auto 5px;
            background: white;
        }
        .actor-body {
            width: 2px;
            height: 25px;
            background: #333;
            margin: 0 auto;
            position: relative;
        }
        .actor-arms {
            position: absolute;
            top: 8px;
            left: -8px;
            width: 18px;
            height: 2px;
            background: #333;
        }
        .actor-legs {
            position: absolute;
            bottom: -15px;
            left: -6px;
            width: 14px;
            height: 15px;
        }
        .actor-legs::before,
        .actor-legs::after {
            content: '';
            position: absolute;
            width: 2px;
            height: 15px;
            background: #333;
        }
        .actor-legs::before {
            left: 2px;
            transform: rotate(-15deg);
        }
        .actor-legs::after {
            right: 2px;
            transform: rotate(15deg);
        }
        
        /* 用例样式 */
        .usecase {
            position: absolute;
            background: #E3F2FD;
            border: 2px solid #2196F3;
            border-radius: 30px;
            padding: 8px 12px;
            font-size: 11px;
            text-align: center;
            min-width: 80px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .usecase-counselor {
            background: #FFF3E0;
            border-color: #FF9800;
        }
        .usecase-admin {
            background: #E8F5E8;
            border-color: #4CAF50;
        }
        
        /* 连接线 */
        .connection-line {
            position: absolute;
            height: 1px;
            background: #333;
            transform-origin: left center;
        }
        
        /* 角色定位 */
        .user-actor { top: 80px; left: 50px; }
        .counselor-actor { top: 280px; left: 50px; }
        .admin-actor { top: 480px; left: 50px; }
        
        /* 用例定位 */
        .uc1 { top: 60px; left: 200px; }
        .uc2 { top: 110px; left: 280px; }
        .uc3 { top: 160px; left: 350px; }
        .uc4 { top: 60px; left: 450px; }
        .uc5 { top: 110px; left: 530px; }
        .uc6 { top: 160px; left: 600px; }
        .uc7 { top: 210px; left: 400px; }
        .uc8 { top: 210px; left: 520px; }
        .uc9 { top: 260px; left: 200px; }
        .uc10 { top: 310px; left: 280px; }
        .uc11 { top: 360px; left: 350px; }
        .uc12 { top: 460px; left: 200px; }
        .uc13 { top: 510px; left: 280px; }
        .uc14 { top: 560px; left: 350px; }
        
        /* 扩展关系箭头 */
        .extend-arrow {
            position: absolute;
            font-size: 10px;
            color: #666;
            font-style: italic;
        }
        .extend1 { top: 85px; left: 500px; }
        .extend2 { top: 185px; left: 570px; }
    </style>
</head>
<body>
    <div class="diagram-container">
        <div class="title">(2) 用例图</div>
        
        <!-- 角色 -->
        <div class="actor user-actor">
            <div class="actor-figure">
                <div class="actor-head"></div>
                <div class="actor-body">
                    <div class="actor-arms"></div>
                    <div class="actor-legs"></div>
                </div>
            </div>
            <div>普通用户</div>
        </div>
        
        <div class="actor counselor-actor">
            <div class="actor-figure">
                <div class="actor-head"></div>
                <div class="actor-body">
                    <div class="actor-arms"></div>
                    <div class="actor-legs"></div>
                </div>
            </div>
            <div>心理辅导员</div>
        </div>
        
        <div class="actor admin-actor">
            <div class="actor-figure">
                <div class="actor-head"></div>
                <div class="actor-body">
                    <div class="actor-arms"></div>
                    <div class="actor-legs"></div>
                </div>
            </div>
            <div>系统管理员</div>
        </div>
        
        <!-- 普通用户用例 -->
        <div class="usecase uc1">用户注册</div>
        <div class="usecase uc2">用户登录</div>
        <div class="usecase uc3">个人信息管理</div>
        <div class="usecase uc4">心理测评</div>
        <div class="usecase uc5">查看测评报告</div>
        <div class="usecase uc6">情绪日记</div>
        <div class="usecase uc7">匿名树洞</div>
        <div class="usecase uc8">互助留言</div>
        
        <!-- 心理辅导员用例 -->
        <div class="usecase usecase-counselor uc9">辅导员管理</div>
        <div class="usecase usecase-counselor uc10">查看用户测评</div>
        <div class="usecase usecase-counselor uc11">提供心理指导</div>
        
        <!-- 系统管理员用例 -->
        <div class="usecase usecase-admin uc12">用户数据管理</div>
        <div class="usecase usecase-admin uc13">内容审核</div>
        <div class="usecase usecase-admin uc14">数据统计分析</div>
        
        <!-- 连接线 (简化显示主要连接) -->
        <div class="connection-line" style="top: 100px; left: 130px; width: 70px;"></div>
        <div class="connection-line" style="top: 130px; left: 130px; width: 150px;"></div>
        <div class="connection-line" style="top: 180px; left: 130px; width: 220px;"></div>
        <div class="connection-line" style="top: 100px; left: 130px; width: 320px;"></div>
        <div class="connection-line" style="top: 130px; left: 130px; width: 400px;"></div>
        <div class="connection-line" style="top: 180px; left: 130px; width: 470px;"></div>
        <div class="connection-line" style="top: 230px; left: 130px; width: 270px;"></div>
        <div class="connection-line" style="top: 230px; left: 130px; width: 390px;"></div>
        
        <div class="connection-line" style="top: 300px; left: 130px; width: 70px;"></div>
        <div class="connection-line" style="top: 330px; left: 130px; width: 150px;"></div>
        <div class="connection-line" style="top: 380px; left: 130px; width: 220px;"></div>
        
        <div class="connection-line" style="top: 500px; left: 130px; width: 70px;"></div>
        <div class="connection-line" style="top: 530px; left: 130px; width: 150px;"></div>
        <div class="connection-line" style="top: 580px; left: 130px; width: 220px;"></div>
        
        <!-- 扩展关系 -->
        <div class="extend-arrow extend1">《extend》</div>
        <div class="extend-arrow extend2">《extend》</div>
        
        <!-- 扩展关系虚线 -->
        <div style="position: absolute; top: 77px; left: 530px; width: 70px; height: 1px; border-top: 1px dashed #666;"></div>
        <div style="position: absolute; top: 177px; left: 600px; width: 50px; height: 1px; border-top: 1px dashed #666; transform: rotate(-20deg);"></div>
    </div>
</body>
</html>
