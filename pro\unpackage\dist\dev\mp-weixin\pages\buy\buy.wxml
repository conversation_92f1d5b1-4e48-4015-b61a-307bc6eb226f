<view class="buy-page"><view class="search-section"><view class="search-container"><u-search vue-id="0ced4a1c-1" placeholder="搜索车名/品牌，如 宝马3系" showAction="{{false}}" bgColor="#f5f5f5" borderColor="transparent" searchIconColor="#1890ff" value="{{keyword}}" data-event-opts="{{[['^change',[['onSearch']]],['^input',[['__set_model',['','keyword','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></u-search></view></view><view class="filter-section"><scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false"><view class="filter-list"><picker mode="selector" range="{{$root.g0}}" data-event-opts="{{[['change',[['onCityChange',['$event']]]]]}}" bindchange="__e"><view class="{{['filter-chip',(selectedCity)?'active':'']}}"><text class="filter-icon">📍</text><text class="filter-text">{{displayCity}}</text><text class="filter-arrow">▼</text></view></picker><picker mode="selector" range="{{$root.g1}}" data-event-opts="{{[['change',[['onPriceChange',['$event']]]]]}}" bindchange="__e"><view class="{{['filter-chip',(selectedPrice)?'active':'']}}"><text class="filter-icon">💰</text><text class="filter-text">{{displayPrice}}</text><text class="filter-arrow">▼</text></view></picker><picker mode="selector" range="{{$root.g2}}" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e"><view class="{{['filter-chip',(selectedType)?'active':'']}}"><text class="filter-icon">🚗</text><text class="filter-text">{{displayType}}</text><text class="filter-arrow">▼</text></view></picker></view></scroll-view></view><view class="result-header"><text class="result-count">{{"找到 "+$root.g3+" 辆车"}}</text><view class="sort-options"><text class="sort-text">排序</text><text class="sort-icon">⇅</text></view></view><view class="car-list"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['viewDetail',['$0'],[[['filteredCars','id',item.$orig.id]]]]]]]}}" class="car-card" bindtap="__e"><view class="car-image-wrapper"><image class="car-image" src="{{item.$orig.image}}" mode="aspectFill"></image><block wx:if="{{item.g4}}"><view class="car-badge">{{item.$orig.tags[0]}}</view></block></view><view class="car-content"><view class="car-header"><text class="car-title">{{item.$orig.name}}</text><text class="car-price">{{"￥"+item.g5}}</text></view><view class="car-meta"><view class="meta-item"><text class="meta-icon">📅</text><text class="meta-text">{{item.$orig.year+"年"}}</text></view><view class="meta-item"><text class="meta-icon">🛣️</text><text class="meta-text">{{item.$orig.mileage+"万公里"}}</text></view><view class="meta-item"><text class="meta-icon">📍</text><text class="meta-text">{{item.$orig.city}}</text></view></view><view class="car-tags"><block wx:for="{{item.l0}}" wx:for-item="tag" wx:for-index="idx" wx:key="idx"><view class="modern-tag">{{''+tag+''}}</view></block></view><block wx:if="{{item.$orig.desc}}"><view class="car-description"><text class="desc-text">{{item.$orig.desc}}</text></view></block></view></view></block></view><block wx:if="{{$root.g6===0}}"><view class="empty-state"><view class="empty-icon">🔍</view><text class="empty-title">未找到匹配的车辆</text><text class="empty-subtitle">试试调整筛选条件</text></view></block></view>