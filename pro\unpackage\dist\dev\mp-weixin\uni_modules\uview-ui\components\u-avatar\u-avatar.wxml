<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="{{['u-avatar','data-v-d3651d6e','u-avatar--'+shape]}}" style="{{$root.s0}}" bindtap="__e"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><block wx:if="{{mpAvatar&&allowMp}}"><open-data style="{{'width:'+($root.g0)+';'+('height:'+($root.g1)+';')}}" type="userAvatarUrl" class="data-v-d3651d6e"></open-data></block><block wx:if="{{mpAvatar&&allowMp}}"></block><block wx:else><block wx:if="{{icon}}"><u-icon vue-id="336138dd-1" name="{{icon}}" size="{{fontSize}}" color="{{color}}" class="data-v-d3651d6e" bind:__l="__l"></u-icon></block><block wx:else><block wx:if="{{text}}"><u--text vue-id="336138dd-2" text="{{text}}" size="{{fontSize}}" color="{{color}}" align="center" customStyle="justify-content: center" class="data-v-d3651d6e" bind:__l="__l"></u--text></block><block wx:else><image class="{{['u-avatar__image','data-v-d3651d6e','u-avatar__image--'+shape]}}" style="{{'width:'+($root.g2)+';'+('height:'+($root.g3)+';')}}" src="{{avatarUrl||defaultUrl}}" mode="{{mode}}" data-event-opts="{{[['error',[['errorHandler',['$event']]]]]}}" binderror="__e"></image></block></block></block></block></view>