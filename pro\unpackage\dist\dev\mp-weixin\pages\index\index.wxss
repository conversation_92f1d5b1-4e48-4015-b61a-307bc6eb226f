@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.index-page {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  min-height: 100vh;
}
/* 顶部搜索区域 */
.header-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 40%, #e6f7ff 100%);
  padding: 0 0 8px;
  border-radius: 0 0 24px 24px;
  position: relative;
  overflow: hidden;
  min-height: 140px;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.08);
}
/* 装饰性背景元素 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.header-decoration .decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.12) 0%, rgba(64, 169, 255, 0.06) 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.08);
}
.header-decoration .decoration-circle.circle-1 {
  width: 100px;
  height: 100px;
  top: -50px;
  right: -25px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.15) 0%, rgba(64, 169, 255, 0.08) 100%);
}
.header-decoration .decoration-circle.circle-2 {
  width: 60px;
  height: 60px;
  top: 30px;
  left: -15px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(64, 169, 255, 0.05) 100%);
}
.header-decoration .decoration-circle.circle-3 {
  width: 40px;
  height: 40px;
  bottom: 15px;
  right: 50px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(64, 169, 255, 0.04) 100%);
}
.header-decoration .decoration-wave {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 20px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23ffffff"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23ffffff"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23ffffff"/></svg>') repeat-x;
  background-size: 1200px 20px;
}
/* 顶部状态栏 */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 12px;
  position: relative;
  z-index: 2;
}
.status-bar .brand-info .brand-name {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
  display: block;
  margin-bottom: 2px;
  text-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}
.status-bar .brand-info .brand-slogan {
  font-size: 12px;
  color: #595959;
  display: block;
}
.status-bar .user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}
.status-bar .user-avatar:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.status-bar .user-avatar .avatar-img {
  width: 100%;
  height: 100%;
}
/* 搜索容器 */
.search-container {
  padding: 0 16px;
  position: relative;
  z-index: 2;
}
.search-container .search-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}
.search-container .search-wrapper .u-search {
  flex: 1;
}
.search-container .search-wrapper .search-filter {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.search-container .search-wrapper .search-filter:active {
  background: rgba(255, 255, 255, 0.3);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.search-container .search-wrapper .search-filter .filter-icon {
  font-size: 16px;
}
.search-container .location-weather {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.search-container .location-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 8px 16px;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.search-container .location-item .location-icon-wrapper {
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}
.search-container .location-item .location-icon {
  font-size: 12px;
}
.search-container .location-item .location-text {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin-right: 6px;
}
.search-container .location-item .dropdown-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
}
.search-container .weather-info {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 8px 16px;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.search-container .weather-info .weather-icon {
  font-size: 16px;
  margin-right: 6px;
}
.search-container .weather-info .weather-text {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}
.search-container .quick-search-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.search-container .quick-search-tags .search-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 6px 12px;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}
.search-container .quick-search-tags .search-tag:active {
  background: rgba(255, 255, 255, 0.3);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.search-container .quick-search-tags .search-tag .tag-text {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
}
/* 轮播区域 */
.banner-section {
  margin: -8px 16px 0;
}
.banner-section .banner-header {
  padding: 16px 0 12px;
  text-align: center;
}
.banner-section .banner-header .banner-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: block;
  margin-bottom: 4px;
}
.banner-section .banner-header .banner-subtitle {
  font-size: 12px;
  color: #8c8c8c;
  display: block;
}
.banner-section .u-swiper {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}
/* 快捷入口 */
.quick-actions {
  margin: 24px 16px;
}
.quick-actions .section-title {
  margin-bottom: 16px;
}
.quick-actions .section-title .title-text {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  display: block;
}
.quick-actions .section-title .title-subtitle {
  font-size: 14px;
  color: #595959;
  margin-top: 4px;
  display: block;
}
.quick-actions .actions-grid {
  display: flex;
  justify-content: center;
  gap: 20px;
}
.quick-actions .action-item {
  flex: 1;
  max-width: 100px;
  background: #ffffff;
  border-radius: 12px;
  padding: 24px 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.quick-actions .action-item:active {
  -webkit-transform: translateY(2px);
          transform: translateY(2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.quick-actions .action-item .action-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin: 0 auto 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.quick-actions .action-item .action-icon-wrapper.buy-icon {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}
.quick-actions .action-item .action-icon-wrapper.sell-icon {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}
.quick-actions .action-item .action-icon-wrapper.valuation-icon {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}
.quick-actions .action-item .action-icon-wrapper.profit-icon {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
}
.quick-actions .action-item .action-icon {
  width: 24px;
  height: 24px;
}
.quick-actions .action-item .action-text {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: block;
  margin-bottom: 4px;
}
.quick-actions .action-item .action-desc {
  font-size: 12px;
  color: #8c8c8c;
  display: block;
}
/* 广告横幅 */
.promo-section {
  margin: 24px 16px;
}
.promo-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}
.promo-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(255, 77, 79, 0.05) 100%);
  pointer-events: none;
}
.promo-card .promo-content {
  flex: 1;
  z-index: 1;
}
.promo-card .promo-content .promo-title {
  font-size: 18px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 8px;
}
.promo-card .promo-content .promo-subtitle {
  font-size: 14px;
  color: #595959;
  margin-bottom: 16px;
}
.promo-card .promo-content .promo-features {
  display: flex;
  gap: 16px;
}
.promo-card .promo-content .promo-features .feature-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.promo-card .promo-content .promo-features .feature-item .feature-icon {
  color: #52c41a;
  font-weight: bold;
}
.promo-card .promo-content .promo-features .feature-item .feature-text {
  font-size: 12px;
  color: #8c8c8c;
}
.promo-card .promo-image {
  width: 80px;
  height: 80px;
  z-index: 1;
}
/* 通用区块样式 */
.section {
  margin: 24px 16px;
}
.section .section-header {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 16px;
}
.section .section-header .section-title .title-text {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  display: block;
}
.section .section-header .section-title .title-subtitle {
  font-size: 14px;
  color: #595959;
  margin-top: 4px;
  display: block;
}
.section .section-header .section-more {
  display: flex;
  align-items: center;
  gap: 4px;
}
.section .section-header .section-more .more-text {
  font-size: 14px;
  color: #1890ff;
}
.section .section-header .section-more .more-icon {
  font-size: 14px;
  color: #1890ff;
}
/* 热门推荐 */
.hot-scroll {
  white-space: nowrap;
}
.hot-list {
  display: flex;
  gap: 12px;
  padding-bottom: 4px;
}
.hot-item {
  width: 160px;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.hot-item:active {
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.hot-item .hot-image-wrapper {
  position: relative;
}
.hot-item .hot-image-wrapper .hot-image {
  width: 100%;
  height: 100px;
}
.hot-item .hot-image-wrapper .hot-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #ffffff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}
.hot-item .hot-content {
  padding: 12px;
}
.hot-item .hot-content .hot-name {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.hot-item .hot-content .hot-meta {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}
.hot-item .hot-content .hot-price {
  font-size: 16px;
  font-weight: 700;
  color: #ff4d4f;
}
/* 猜你喜欢 */
.like-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.like-item {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 16px;
  transition: all 0.3s ease;
}
.like-item:active {
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.like-item .like-image-wrapper .like-image {
  width: 120px;
  height: 90px;
  border-radius: 8px;
}
.like-item .like-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.like-item .like-content .like-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}
.like-item .like-content .like-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}
.like-item .like-content .like-tags .modern-tag {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
}
.like-item .like-content .like-location {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}
.like-item .like-content .like-price-row {
  display: flex;
  align-items: center;
  gap: 8px;
}
.like-item .like-content .like-price-row .like-price {
  font-size: 18px;
  font-weight: 700;
  color: #ff4d4f;
}
.like-item .like-content .like-price-row .like-original-price {
  font-size: 14px;
  color: #bfbfbf;
  text-decoration: line-through;
}
