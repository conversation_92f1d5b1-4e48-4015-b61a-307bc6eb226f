<view class="my-page"><view class="profile-section"><view class="profile-header"><view class="profile-avatar"><u-avatar vue-id="704e9d00-1" src="{{avatarUrl}}" size="80" bind:__l="__l"></u-avatar><view class="avatar-badge">VIP</view></view><view class="profile-info"><text class="profile-name">{{userName}}</text><text class="profile-phone">{{maskedPhone}}</text><view class="profile-stats"><view class="stat-item"><text class="stat-number">12</text><text class="stat-label">关注车辆</text></view><view class="stat-divider"></view><view class="stat-item"><text class="stat-number">3</text><text class="stat-label">交易记录</text></view></view></view><view data-event-opts="{{[['tap',[['editProfile',['$event']]]]]}}" class="profile-edit" bindtap="__e"><text class="edit-icon">✏️</text></view></view></view><view class="quick-functions"><view class="function-grid"><view data-event-opts="{{[['tap',[['goToBuyOrders',['$event']]]]]}}" class="function-item" bindtap="__e"><view class="function-icon buy-icon"><text class="icon-text">🛒</text></view><text class="function-text">买车订单</text></view><view data-event-opts="{{[['tap',[['goToSellOrders',['$event']]]]]}}" class="function-item" bindtap="__e"><view class="function-icon sell-icon"><text class="icon-text">💰</text></view><text class="function-text">卖车订单</text></view><view data-event-opts="{{[['tap',[['goFavorites',['$event']]]]]}}" class="function-item" bindtap="__e"><view class="function-icon favorite-icon"><text class="icon-text">❤️</text></view><text class="function-text">我的收藏</text></view><view data-event-opts="{{[['tap',[['goHistory',['$event']]]]]}}" class="function-item" bindtap="__e"><view class="function-icon history-icon"><text class="icon-text">📋</text></view><text class="function-text">浏览记录</text></view></view></view><view class="section-card"><view class="section-header"><view class="section-title"><text class="title-text">我的订单</text><text class="title-subtitle">查看交易进度</text></view><view data-event-opts="{{[['tap',[['goToBuyOrders',['$event']]]]]}}" class="section-more" bindtap="__e"><text class="more-text">查看全部</text><text class="more-icon">→</text></view></view><view class="order-list"><view data-event-opts="{{[['tap',[['goToBuyOrders',['$event']]]]]}}" class="order-item" bindtap="__e"><view class="order-icon"><text class="icon-emoji">🚗</text></view><view class="order-content"><text class="order-title">买车订单</text><text class="order-desc">近期3笔订单可查看</text></view><view class="order-arrow">→</view></view><view data-event-opts="{{[['tap',[['goToSellOrders',['$event']]]]]}}" class="order-item" bindtap="__e"><view class="order-icon"><text class="icon-emoji">💸</text></view><view class="order-content"><text class="order-title">卖车订单</text><text class="order-desc">成交、洽谈中等状态</text></view><view class="order-arrow">→</view></view></view></view><view class="section-card"><view class="section-header"><view class="section-title"><text class="title-text">功能服务</text><text class="title-subtitle">更多实用功能</text></view></view><view class="service-list"><view data-event-opts="{{[['tap',[['goAbout',['$event']]]]]}}" class="service-item" bindtap="__e"><view class="service-icon"><text class="icon-emoji">ℹ️</text></view><view class="service-content"><text class="service-title">关于我们</text><text class="service-desc">铜墙铁壁二手车</text></view><view class="service-arrow">→</view></view><view data-event-opts="{{[['tap',[['goFeedback',['$event']]]]]}}" class="service-item" bindtap="__e"><view class="service-icon"><text class="icon-emoji">💬</text></view><view class="service-content"><text class="service-title">在线客服反馈</text><text class="service-desc">提交问题与建议</text></view><view class="service-arrow">→</view></view></view></view></view>