<u-transition vue-id="a1aec192-1" mode="fade" show="{{show}}" class="data-v-3732d7af" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-tag-wrapper data-v-3732d7af"><view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="{{['u-tag','data-v-3732d7af','u-tag--'+shape,!plain&&'u-tag--'+type,plain&&'u-tag--'+type+'--plain','u-tag--'+size,plain&&plainFill&&'u-tag--'+type+'--plain--fill']}}" style="{{$root.s0}}" catchtap="__e"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><block wx:if="{{icon}}"><view class="u-tag__icon data-v-3732d7af"><block wx:if="{{$root.g0}}"><image style="{{$root.s1}}" src="{{icon}}" class="data-v-3732d7af"></image></block><block wx:else><u-icon vue-id="{{('a1aec192-2')+','+('a1aec192-1')}}" color="{{elIconColor}}" name="{{icon}}" size="{{iconSize}}" class="data-v-3732d7af" bind:__l="__l"></u-icon></block></view></block></block><text class="{{['u-tag__text','data-v-3732d7af','u-tag__text--'+type,plain&&'u-tag__text--'+type+'--plain','u-tag__text--'+size]}}" style="{{$root.s2}}">{{text}}</text></view><block wx:if="{{closable}}"><view data-event-opts="{{[['tap',[['closeHandler',['$event']]]]]}}" class="{{['u-tag__close','data-v-3732d7af','u-tag__close--'+size]}}" style="{{'background-color:'+(closeColor)+';'}}" catchtap="__e"><u-icon vue-id="{{('a1aec192-3')+','+('a1aec192-1')}}" name="close" size="{{closeSize}}" color="#ffffff" class="data-v-3732d7af" bind:__l="__l"></u-icon></view></block></view></u-transition>