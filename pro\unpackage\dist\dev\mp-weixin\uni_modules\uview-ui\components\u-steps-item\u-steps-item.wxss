@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
view.data-v-3ae6176e, scroll-view.data-v-3ae6176e, swiper-item.data-v-3ae6176e {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-steps-item.data-v-3ae6176e {
  flex: 1;

  display: flex;

  flex-direction: row;
}
.u-steps-item--row.data-v-3ae6176e {
  flex-direction: column;
  align-items: center;
  position: relative;
}
.u-steps-item--column.data-v-3ae6176e {
  position: relative;
  flex-direction: row;
  justify-content: flex-start;
  padding-bottom: 5px;
}
.u-steps-item__wrapper.data-v-3ae6176e {

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: #fff;
}
.u-steps-item__wrapper--column.data-v-3ae6176e {
  width: 20px;
  height: 32px;
}
.u-steps-item__wrapper--column--dot.data-v-3ae6176e {
  height: 20px;
  width: 20px;
}
.u-steps-item__wrapper--row.data-v-3ae6176e {
  width: 32px;
  height: 20px;
}
.u-steps-item__wrapper--row--dot.data-v-3ae6176e {
  width: 20px;
  height: 20px;
}
.u-steps-item__wrapper__circle.data-v-3ae6176e {
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  flex-shrink: 0;
  border-radius: 100px;
  border-width: 1px;
  border-color: #909193;
  border-style: solid;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}
.u-steps-item__wrapper__circle__text.data-v-3ae6176e {
  color: #909193;
  font-size: 11px;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 11px;
}
.u-steps-item__wrapper__dot.data-v-3ae6176e {
  width: 10px;
  height: 10px;
  border-radius: 100px;
  background-color: #606266;
}
.u-steps-item__content.data-v-3ae6176e {

  display: flex;

  flex-direction: row;
  flex: 1;
}
.u-steps-item__content--row.data-v-3ae6176e {
  flex-direction: column;
  align-items: center;
}
.u-steps-item__content--column.data-v-3ae6176e {
  flex-direction: column;
  margin-left: 6px;
}
.u-steps-item__line.data-v-3ae6176e {
  position: absolute;
  background: #909193;
}
.u-steps-item__line--row.data-v-3ae6176e {
  top: 10px;
  height: 1px;
}
.u-steps-item__line--column.data-v-3ae6176e {
  width: 1px;
  left: 10px;
}
