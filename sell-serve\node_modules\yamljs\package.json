{"name": "yamljs", "version": "0.1.6", "description": "Standalone JavaScript YAML 1.2 Parser & Encoder. Works under node.js and all major browsers. Also brings command line YAML/JSON conversion tools.", "keywords": ["yaml", "json", "yaml2j<PERSON>", "json2yaml"], "author": "<PERSON> <<EMAIL>>", "main": "./bin/yaml.js", "dependencies": {"argparse": "~0.1.4", "glob": "~3.1.11"}, "bin": {"yaml2json": "./bin/yaml2json", "json2yaml": "./bin/json2yaml"}, "devDependencies": {}, "repository": {"type": "git", "url": "git://github.com/jeremyfa/yaml.js.git"}}