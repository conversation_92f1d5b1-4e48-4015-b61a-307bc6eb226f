@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
.sell-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 80px;
}
/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px 30px;
  text-align: center;
  color: white;
}
.header-section .main-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  display: block;
}
.header-section .sub-title {
  font-size: 16px;
  opacity: 0.9;
  display: block;
}
/* 表单包装器 */
.form-wrapper {
  padding: 20px;
  gap: 20px;
  display: flex;
  flex-direction: column;
}
/* 信息区块 */
.info-block {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}
.info-block .block-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
}
.info-block .block-title .title-icon {
  font-size: 24px;
  margin-right: 10px;
}
.info-block .block-title .title-text {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}
/* 输入网格 */
.input-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.input-row {
  display: flex;
  gap: 12px;
}
.input-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.input-item.full {
  flex: 1 1 100%;
}
.input-item .label {
  font-size: 16px;
  font-weight: 500;
  color: #34495e;
}
/* 输入框样式 */
.input-box {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 14px 16px;
  transition: all 0.3s ease;
}
.input-box:focus-within {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
.input-box .text-input {
  width: 100%;
  font-size: 16px;
  color: #2c3e50;
  background: transparent;
  border: none;
  outline: none;
}
.input-box .text-input::-webkit-input-placeholder {
  color: #95a5a6;
}
.input-box .text-input::placeholder {
  color: #95a5a6;
}
.input-box.price-input {
  display: flex;
  align-items: center;
  gap: 8px;
}
.input-box.price-input .price-unit {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}
/* 选择器样式 */
.picker-box {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 14px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}
.picker-box:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  border-color: #667eea;
}
.picker-box .picker-text {
  font-size: 16px;
  color: #2c3e50;
}
.picker-box .picker-arrow {
  font-size: 12px;
  color: #95a5a6;
}
/* 选项组样式 */
.option-group {
  display: flex;
  gap: 8px;
}
.option-item {
  flex: 1;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 16px 12px;
  text-align: center;
  transition: all 0.3s ease;
}
.option-item.selected {
  background: #667eea;
  border-color: #667eea;
  color: white;
}
.option-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.option-item .option-emoji {
  font-size: 24px;
  display: block;
  margin-bottom: 6px;
}
.option-item .option-label {
  font-size: 14px;
  font-weight: 500;
}
/* 切换组样式 */
.toggle-group {
  display: flex;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 4px;
  border: 2px solid #e9ecef;
}
.toggle-item {
  flex: 1;
  padding: 12px;
  text-align: center;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #7f8c8d;
  transition: all 0.3s ease;
}
.toggle-item.active {
  background: #667eea;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}
/* 底部按钮栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px 20px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}
.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.submit-btn:active {
  -webkit-transform: translateY(2px);
          transform: translateY(2px);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}
.submit-btn .btn-text {
  font-size: 18px;
  font-weight: 600;
  color: white;
}
