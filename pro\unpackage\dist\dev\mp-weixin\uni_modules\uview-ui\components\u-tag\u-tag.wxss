@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 现代化主题色彩系统 ==================== */
/* 主色调 - 蓝色系 */
/* 辅助色 - 红色系 */
/* 中性色 - 黑白灰系 */
/* 背景色 */
/* 功能色 */
/* 边框和分割线 */
/* 阴影 */
/* 圆角 */
/* 间距 */
view.data-v-3732d7af, scroll-view.data-v-3732d7af, swiper-item.data-v-3732d7af {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-tag-wrapper.data-v-3732d7af {
  position: relative;
}
.u-tag.data-v-3732d7af {

  display: flex;

  flex-direction: row;
  align-items: center;
  border-style: solid;
}
.u-tag--circle.data-v-3732d7af {
  border-radius: 100px;
}
.u-tag--square.data-v-3732d7af {
  border-radius: 3px;
}
.u-tag__icon.data-v-3732d7af {
  margin-right: 4px;
}
.u-tag__text--mini.data-v-3732d7af {
  font-size: 12px;
  line-height: 12px;
}
.u-tag__text--medium.data-v-3732d7af {
  font-size: 13px;
  line-height: 13px;
}
.u-tag__text--large.data-v-3732d7af {
  font-size: 15px;
  line-height: 15px;
}
.u-tag--mini.data-v-3732d7af {
  height: 22px;
  line-height: 22px;
  padding: 0 5px;
}
.u-tag--medium.data-v-3732d7af {
  height: 26px;
  line-height: 22px;
  padding: 0 10px;
}
.u-tag--large.data-v-3732d7af {
  height: 32px;
  line-height: 32px;
  padding: 0 15px;
}
.u-tag--primary.data-v-3732d7af {
  background-color: #3c9cff;
  border-width: 1px;
  border-color: #3c9cff;
}
.u-tag--primary--plain.data-v-3732d7af {
  border-width: 1px;
  border-color: #3c9cff;
}
.u-tag--primary--plain--fill.data-v-3732d7af {
  background-color: #ecf5ff;
}
.u-tag__text--primary.data-v-3732d7af {
  color: #FFFFFF;
}
.u-tag__text--primary--plain.data-v-3732d7af {
  color: #3c9cff;
}
.u-tag--error.data-v-3732d7af {
  background-color: #f56c6c;
  border-width: 1px;
  border-color: #f56c6c;
}
.u-tag--error--plain.data-v-3732d7af {
  border-width: 1px;
  border-color: #f56c6c;
}
.u-tag--error--plain--fill.data-v-3732d7af {
  background-color: #fef0f0;
}
.u-tag__text--error.data-v-3732d7af {
  color: #FFFFFF;
}
.u-tag__text--error--plain.data-v-3732d7af {
  color: #f56c6c;
}
.u-tag--warning.data-v-3732d7af {
  background-color: #f9ae3d;
  border-width: 1px;
  border-color: #f9ae3d;
}
.u-tag--warning--plain.data-v-3732d7af {
  border-width: 1px;
  border-color: #f9ae3d;
}
.u-tag--warning--plain--fill.data-v-3732d7af {
  background-color: #fdf6ec;
}
.u-tag__text--warning.data-v-3732d7af {
  color: #FFFFFF;
}
.u-tag__text--warning--plain.data-v-3732d7af {
  color: #f9ae3d;
}
.u-tag--success.data-v-3732d7af {
  background-color: #5ac725;
  border-width: 1px;
  border-color: #5ac725;
}
.u-tag--success--plain.data-v-3732d7af {
  border-width: 1px;
  border-color: #5ac725;
}
.u-tag--success--plain--fill.data-v-3732d7af {
  background-color: #f5fff0;
}
.u-tag__text--success.data-v-3732d7af {
  color: #FFFFFF;
}
.u-tag__text--success--plain.data-v-3732d7af {
  color: #5ac725;
}
.u-tag--info.data-v-3732d7af {
  background-color: #909399;
  border-width: 1px;
  border-color: #909399;
}
.u-tag--info--plain.data-v-3732d7af {
  border-width: 1px;
  border-color: #909399;
}
.u-tag--info--plain--fill.data-v-3732d7af {
  background-color: #f4f4f5;
}
.u-tag__text--info.data-v-3732d7af {
  color: #FFFFFF;
}
.u-tag__text--info--plain.data-v-3732d7af {
  color: #909399;
}
.u-tag__close.data-v-3732d7af {
  position: absolute;
  z-index: 999;
  top: 10px;
  right: 10px;
  border-radius: 100px;
  background-color: #C6C7CB;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: center;
  -webkit-transform: scale(0.6) translate(80%, -80%);
          transform: scale(0.6) translate(80%, -80%);
}
.u-tag__close--mini.data-v-3732d7af {
  width: 18px;
  height: 18px;
}
.u-tag__close--medium.data-v-3732d7af {
  width: 22px;
  height: 22px;
}
.u-tag__close--large.data-v-3732d7af {
  width: 25px;
  height: 25px;
}
