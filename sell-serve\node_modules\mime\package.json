{"author": {"name": "<PERSON>", "url": "http://github.com/broofa", "email": "<EMAIL>"}, "bin": {"mime": "cli.js"}, "contributors": [{"name": "<PERSON>", "url": "http://github.com/bentomas", "email": "<EMAIL>"}], "description": "A comprehensive library for mime-type mapping", "license": "MIT", "dependencies": {}, "devDependencies": {"mime-db": "1.30.0"}, "scripts": {"prepublish": "node build/build.js > types.json", "test": "node build/test.js"}, "keywords": ["util", "mime"], "main": "mime.js", "name": "mime", "repository": {"url": "https://github.com/broofa/node-mime", "type": "git"}, "version": "1.4.1"}