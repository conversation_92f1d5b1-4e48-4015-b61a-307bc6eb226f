<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心理健康关怀平台 - 系统架构图和用例图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
            line-height: 1.4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        h2 {
            color: #333;
            margin: 30px 0 20px 0;
            font-size: 18px;
        }
        
        /* 总体结构图样式 */
        .structure-diagram {
            text-align: center;
            margin: 30px 0;
        }
        .user-types {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        .user-box {
            width: 80px;
            height: 100px;
            border: 2px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            font-size: 12px;
            writing-mode: vertical-rl;
            text-orientation: upright;
        }
        .platform-box {
            width: 200px;
            height: 60px;
            border: 2px solid #333;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f0f0;
            font-weight: bold;
        }
        .management-layer {
            width: 120px;
            height: 50px;
            border: 2px solid #333;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .modules-container {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 30px 0;
        }
        .module-group {
            text-align: center;
        }
        .module-title {
            font-weight: bold;
            margin-bottom: 15px;
            padding: 8px;
            background: #e8e8e8;
            border: 1px solid #333;
        }
        .module-items {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .module-item {
            width: 80px;
            height: 80px;
            border: 1px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            font-size: 11px;
            writing-mode: vertical-rl;
            text-orientation: upright;
        }
        .arrow {
            font-size: 20px;
            margin: 10px 0;
            color: #666;
        }
        
        /* 用例图样式 */
        .usecase-diagram {
            position: relative;
            height: 600px;
            margin: 40px 0;
            border: 1px solid #ddd;
            background: #fafafa;
        }
        .actor {
            position: absolute;
            width: 80px;
            height: 100px;
            text-align: center;
            font-size: 12px;
        }
        .actor-icon {
            width: 30px;
            height: 60px;
            margin: 0 auto 5px;
            background: #4CAF50;
            border-radius: 15px 15px 0 0;
            position: relative;
        }
        .actor-icon::before {
            content: '';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 20px;
            background: #4CAF50;
            border-radius: 50%;
        }
        .actor-icon::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: #4CAF50;
        }
        .usecase {
            position: absolute;
            background: #E3F2FD;
            border: 2px solid #2196F3;
            border-radius: 50px;
            padding: 8px 15px;
            font-size: 11px;
            text-align: center;
            min-width: 80px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .line {
            position: absolute;
            height: 1px;
            background: #666;
            transform-origin: left center;
        }
        
        /* 定位用例和角色 */
        .user-actor { top: 50px; left: 50px; }
        .counselor-actor { top: 300px; left: 50px; }
        .admin-actor { top: 500px; left: 50px; }
        
        .uc1 { top: 80px; left: 200px; }
        .uc2 { top: 130px; left: 250px; }
        .uc3 { top: 180px; left: 300px; }
        .uc4 { top: 80px; left: 400px; }
        .uc5 { top: 130px; left: 450px; }
        .uc6 { top: 180px; left: 500px; }
        .uc7 { top: 230px; left: 350px; }
        .uc8 { top: 280px; left: 400px; }
        .uc9 { top: 330px; left: 300px; }
        .uc10 { top: 380px; left: 350px; }
        .uc11 { top: 430px; left: 400px; }
        .uc12 { top: 480px; left: 300px; }
        .uc13 { top: 530px; left: 350px; }
        
        .counselor-usecase { background: #FFF3E0; border-color: #FF9800; }
        .admin-usecase { background: #E8F5E8; border-color: #4CAF50; }
    </style>
</head>
<body>
    <div class="container">
        <h1>心理健康关怀平台设计</h1>
        
        <h2>(1) 总体结构</h2>
        <p>系统分为三类用户，每个类用户对应不同的功能权限。</p>
        
        <div class="structure-diagram">
            <!-- 用户类型 -->
            <div class="user-types">
                <div class="user-box">普通用户</div>
                <div class="user-box">心理辅导员</div>
                <div class="user-box">后台管理员</div>
                <div class="user-box">游客用户</div>
                <div class="user-box">系统维护员</div>
            </div>
            
            <div class="arrow">↓</div>
            
            <!-- 平台核心 -->
            <div class="platform-box">心理健康关怀平台</div>
            
            <div class="arrow">↓</div>
            
            <!-- 管理层 -->
            <div class="management-layer">管理层</div>
            
            <div class="arrow">↓</div>
            
            <!-- 功能模块 -->
            <div class="modules-container">
                <div class="module-group">
                    <div class="module-title">用户管理</div>
                    <div class="module-items">
                        <div class="module-item">用户注册</div>
                        <div class="module-item">用户登录</div>
                        <div class="module-item">角色管理</div>
                        <div class="module-item">权限控制</div>
                        <div class="module-item">个人信息</div>
                    </div>
                </div>
                
                <div class="module-group">
                    <div class="module-title">测评管理</div>
                    <div class="module-items">
                        <div class="module-item">量表管理</div>
                        <div class="module-item">测评执行</div>
                        <div class="module-item">结果分析</div>
                        <div class="module-item">报告生成</div>
                        <div class="module-item">历史记录</div>
                        <div class="module-item">数据统计</div>
                        <div class="module-item">趋势分析</div>
                    </div>
                </div>
                
                <div class="module-group">
                    <div class="module-title">其他功能</div>
                    <div class="module-items">
                        <div class="module-item">情绪日记</div>
                        <div class="module-item">匿名树洞</div>
                        <div class="module-item">互助社区</div>
                        <div class="module-item">知识推送</div>
                        <div class="module-item">案例分享</div>
                        <div class="module-item">内容审核</div>
                        <div class="module-item">系统管理</div>
                    </div>
                </div>
            </div>
        </div>
        
        <h2>(2) 用例图</h2>
        
        <div class="usecase-diagram">
            <!-- 角色 -->
            <div class="actor user-actor">
                <div class="actor-icon"></div>
                <div>普通用户</div>
            </div>
            
            <div class="actor counselor-actor">
                <div class="actor-icon"></div>
                <div>心理辅导员</div>
            </div>
            
            <div class="actor admin-actor">
                <div class="actor-icon"></div>
                <div>系统管理员</div>
            </div>
            
            <!-- 用例 -->
            <div class="usecase uc1">用户注册</div>
            <div class="usecase uc2">用户登录</div>
            <div class="usecase uc3">个人信息管理</div>
            <div class="usecase uc4">心理测评</div>
            <div class="usecase uc5">查看测评报告</div>
            <div class="usecase uc6">情绪日记</div>
            <div class="usecase uc7">匿名树洞</div>
            <div class="usecase uc8">互助留言</div>
            <div class="usecase uc9 counselor-usecase">辅导员管理</div>
            <div class="usecase uc10 counselor-usecase">查看用户测评</div>
            <div class="usecase uc11 counselor-usecase">提供心理指导</div>
            <div class="usecase uc12 admin-usecase">用户数据管理</div>
            <div class="usecase uc13 admin-usecase">内容审核</div>
            
            <!-- 连接线 (简化显示，实际项目中可用SVG或Canvas绘制) -->
            <div style="position: absolute; top: 100px; left: 130px; width: 70px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 150px; left: 130px; width: 120px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 200px; left: 130px; width: 170px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 100px; left: 130px; width: 270px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 150px; left: 130px; width: 320px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 200px; left: 130px; width: 370px; height: 1px; background: #666;"></div>
            
            <div style="position: absolute; top: 350px; left: 130px; width: 170px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 400px; left: 130px; width: 220px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 450px; left: 130px; width: 270px; height: 1px; background: #666;"></div>
            
            <div style="position: absolute; top: 520px; left: 130px; width: 170px; height: 1px; background: #666;"></div>
            <div style="position: absolute; top: 550px; left: 130px; width: 220px; height: 1px; background: #666;"></div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f0f8ff; border-radius: 8px; border: 1px solid #4682b4;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">功能说明</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; font-size: 14px;">
                <div>
                    <strong>普通用户功能：</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>用户注册登录</li>
                        <li>心理测评及报告查看</li>
                        <li>情绪日记记录</li>
                        <li>匿名树洞倾诉</li>
                        <li>互助社区参与</li>
                        <li>心理知识浏览</li>
                    </ul>
                </div>
                <div>
                    <strong>心理辅导员功能：</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>专业资料管理</li>
                        <li>查看用户测评结果</li>
                        <li>提供心理指导建议</li>
                        <li>案例分享与交流</li>
                        <li>专业知识发布</li>
                    </ul>
                </div>
                <div>
                    <strong>系统管理员功能：</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>用户数据管理</li>
                        <li>内容审核管理</li>
                        <li>数据统计分析</li>
                        <li>系统配置管理</li>
                        <li>安全监控</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px; border: 1px solid #4caf50;">
            <h3 style="color: #2c3e50; margin-bottom: 10px;">技术实现特点</h3>
            <div style="font-size: 14px; line-height: 1.6;">
                • <strong>前端技术：</strong>Vue.js框架，响应式设计，良好的用户交互体验<br>
                • <strong>后端技术：</strong>Spring Boot框架，负责业务逻辑处理与数据库交互<br>
                • <strong>数据库设计：</strong>用户信息、心理测评结果、情绪日记、社区互动等数据表<br>
                • <strong>系统架构：</strong>前后端分离模式，提高开发效率和系统可维护性<br>
                • <strong>安全保障：</strong>用户隐私保护，匿名功能支持，数据加密存储
            </div>
        </div>
    </div>
</body>
</html>
