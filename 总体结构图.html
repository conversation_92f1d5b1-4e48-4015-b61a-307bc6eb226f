<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心理健康关怀平台 - 总体结构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .diagram-container {
            width: 800px;
            height: 600px;
            border: 2px solid #333;
            background: white;
            position: relative;
            padding: 20px;
            box-sizing: border-box;
        }
        .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .subtitle {
            text-align: center;
            font-size: 14px;
            margin-bottom: 30px;
            color: #666;
        }
        
        /* 用户层 */
        .user-layer {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 25px;
        }
        .user-box {
            width: 70px;
            height: 90px;
            border: 2px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            font-size: 12px;
            writing-mode: vertical-rl;
            text-orientation: upright;
            font-weight: bold;
        }
        
        /* 平台核心 */
        .platform-box {
            width: 220px;
            height: 50px;
            border: 2px solid #333;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
            font-weight: bold;
            font-size: 14px;
        }
        
        /* 管理层 */
        .management-box {
            width: 100px;
            height: 40px;
            border: 2px solid #333;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            font-weight: bold;
            font-size: 13px;
        }
        
        /* 功能模块 */
        .modules-container {
            display: flex;
            justify-content: center;
            gap: 40px;
        }
        .module-group {
            text-align: center;
        }
        .module-title {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 8px 15px;
            background: #e8e8e8;
            border: 2px solid #333;
            font-size: 13px;
        }
        .module-items {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }
        .module-item {
            width: 70px;
            height: 35px;
            border: 1px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            font-size: 10px;
            writing-mode: vertical-rl;
            text-orientation: upright;
        }
        
        /* 箭头 */
        .arrow {
            text-align: center;
            font-size: 20px;
            margin: 8px 0;
            color: #333;
            font-weight: bold;
        }
        
        /* 连接线 */
        .line-vertical {
            width: 2px;
            background: #333;
            margin: 0 auto;
        }
        .line-horizontal {
            height: 2px;
            background: #333;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="diagram-container">
        <div class="title">(1) 总体结构</div>
        <div class="subtitle">系统分为三类用户，每个类用户对应不同的功能权限。</div>
        
        <!-- 用户层 -->
        <div class="user-layer">
            <div class="user-box">普通用户</div>
            <div class="user-box">心理辅导员</div>
            <div class="user-box">后台管理员</div>
            <div class="user-box">游客用户</div>
            <div class="user-box">系统维护员</div>
        </div>
        
        <div class="arrow">↓</div>
        
        <!-- 平台核心 -->
        <div class="platform-box">心理健康关怀平台系统</div>
        
        <div class="arrow">↓</div>
        
        <!-- 管理层 -->
        <div class="management-box">管理层</div>
        
        <div class="arrow">↓</div>
        
        <!-- 功能模块 -->
        <div class="modules-container">
            <div class="module-group">
                <div class="module-title">用户管理</div>
                <div class="module-items">
                    <div class="module-item">用户注册</div>
                    <div class="module-item">用户登录</div>
                    <div class="module-item">角色管理</div>
                    <div class="module-item">权限控制</div>
                    <div class="module-item">个人信息</div>
                </div>
            </div>
            
            <div class="module-group">
                <div class="module-title">测评管理</div>
                <div class="module-items">
                    <div class="module-item">量表管理</div>
                    <div class="module-item">测评执行</div>
                    <div class="module-item">结果分析</div>
                    <div class="module-item">报告生成</div>
                    <div class="module-item">历史记录</div>
                    <div class="module-item">数据统计</div>
                    <div class="module-item">趋势分析</div>
                </div>
            </div>
            
            <div class="module-group">
                <div class="module-title">其他功能</div>
                <div class="module-items">
                    <div class="module-item">情绪日记</div>
                    <div class="module-item">匿名树洞</div>
                    <div class="module-item">互助社区</div>
                    <div class="module-item">知识推送</div>
                    <div class="module-item">案例分享</div>
                    <div class="module-item">内容审核</div>
                    <div class="module-item">系统管理</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
